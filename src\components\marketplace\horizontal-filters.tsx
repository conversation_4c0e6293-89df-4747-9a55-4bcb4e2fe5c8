'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Slider } from '@/components/ui/slider';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Checkbox } from '@/components/ui/checkbox';
import { X, Info, ChevronDown } from 'lucide-react';
import { SearchFilters } from '@/lib/marketplace';

interface HorizontalFiltersProps {
  filters: SearchFilters;
  onFiltersChange: (filters: SearchFilters) => void;
  onClearFilters: () => void;
}

export default function HorizontalFilters({
  filters,
  onFiltersChange,
  onClearFilters,
}: HorizontalFiltersProps) {
  const [priceRange, setPriceRange] = useState([
    filters.min_price || 0,
    filters.max_price || 1000,
  ]);
  const [ageRange, setAgeRange] = useState([
    filters.min_age || 18,
    filters.max_age || 65,
  ]);

  // Mock data - ovo će biti zamenjen sa API pozivima
  const platforms = [
    { id: 1, name: 'Instagram', icon: '📷' },
    { id: 2, name: 'YouTube', icon: '📺' },
    { id: 3, name: 'TikTok', icon: '🎵' },
    { id: 4, name: 'Facebook', icon: '📘' },
    { id: 5, name: 'Twitter', icon: '🐦' },
    { id: 6, name: 'LinkedIn', icon: '💼' },
  ];

  const contentTypes = [
    { id: 1, name: 'Post', platforms: ['Instagram', 'Facebook', 'LinkedIn'] },
    { id: 2, name: 'Story', platforms: ['Instagram', 'Facebook'] },
    { id: 3, name: 'Reel', platforms: ['Instagram'] },
    { id: 4, name: 'Video', platforms: ['YouTube', 'TikTok', 'Facebook'] },
    { id: 5, name: 'Short', platforms: ['YouTube', 'TikTok'] },
    {
      id: 6,
      name: 'Live',
      platforms: ['Instagram', 'YouTube', 'TikTok', 'Facebook'],
    },
  ];

  const categories = [
    { id: 1, name: 'Fashion', icon: '👗' },
    { id: 2, name: 'Beauty', icon: '💄' },
    { id: 3, name: 'Fitness', icon: '💪' },
    { id: 4, name: 'Food', icon: '🍕' },
    { id: 5, name: 'Travel', icon: '✈️' },
    { id: 6, name: 'Tech', icon: '📱' },
    { id: 7, name: 'Lifestyle', icon: '🌟' },
    { id: 8, name: 'Gaming', icon: '🎮' },
  ];

  const updateFilters = (key: keyof SearchFilters, value: any) => {
    onFiltersChange({
      ...filters,
      [key]: value,
    });
  };

  const toggleArrayFilter = (key: keyof SearchFilters, value: string) => {
    const currentArray = (filters[key] as string[]) || [];
    const newArray = currentArray.includes(value)
      ? currentArray.filter(item => item !== value)
      : [...currentArray, value];

    updateFilters(key, newArray);
  };

  const handlePriceChange = (values: number[]) => {
    setPriceRange(values);
    updateFilters('min_price', values[0]);
    updateFilters('max_price', values[1]);
  };

  const handleAgeChange = (values: number[]) => {
    setAgeRange(values);
    updateFilters('min_age', values[0]);
    updateFilters('max_age', values[1]);
  };

  // Računanje broja aktivnih filtera
  const getActiveFiltersCount = () => {
    let count = 0;
    if (filters.categories?.length) count += filters.categories.length;
    if (filters.platforms?.length) count += filters.platforms.length;
    if (filters.content_types?.length) count += filters.content_types.length;
    if (filters.min_price !== undefined || filters.max_price !== undefined)
      count += 1;
    if (filters.gender) count += 1;
    if (filters.min_age !== undefined || filters.max_age !== undefined)
      count += 1;
    return count;
  };

  const activeFiltersCount = getActiveFiltersCount();

  return (
    <TooltipProvider>
      <div className="bg-card border rounded-lg p-4 mb-6">
        {/* Header */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-2">
            <h3 className="font-medium">
              Gdje želite da se objavi vaš sadržaj?
            </h3>
            <Tooltip>
              <TooltipTrigger>
                <Info className="h-4 w-4 text-muted-foreground" />
              </TooltipTrigger>
              <TooltipContent>
                <p>Odaberite platforme i tip sadržaja za vašu kampanju</p>
              </TooltipContent>
            </Tooltip>
          </div>
          {activeFiltersCount > 0 && (
            <Button variant="ghost" size="sm" onClick={onClearFilters}>
              <X className="h-4 w-4 mr-1" />
              Obriši sve ({activeFiltersCount})
            </Button>
          )}
        </div>

        {/* Filters Row */}
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-3">
          {/* Platform Filter */}
          <Popover>
            <PopoverTrigger asChild>
              <div className="inline-flex items-center justify-between h-9 px-3 py-2 text-sm font-medium border border-input bg-background rounded-md cursor-pointer hover:bg-accent hover:text-accent-foreground">
                <span>Platforma</span>
                <div className="flex items-center gap-2">
                  {filters.platforms?.length ? (
                    <Badge variant="secondary" className="text-xs">
                      {filters.platforms.length}
                    </Badge>
                  ) : null}
                  <ChevronDown className="h-4 w-4" />
                </div>
              </div>
            </PopoverTrigger>
            <PopoverContent className="w-64" align="start">
              <div className="space-y-2">
                <h4 className="font-medium text-sm">Odaberite platforme</h4>
                {platforms.map(platform => (
                  <div
                    key={platform.id}
                    className="flex items-center space-x-2"
                  >
                    <Checkbox
                      id={`platform-${platform.id}`}
                      checked={
                        filters.platforms?.includes(platform.name) || false
                      }
                      onCheckedChange={() =>
                        toggleArrayFilter('platforms', platform.name)
                      }
                    />
                    <label
                      htmlFor={`platform-${platform.id}`}
                      className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 flex items-center gap-2"
                    >
                      <span>{platform.icon}</span>
                      {platform.name}
                    </label>
                  </div>
                ))}
              </div>
            </PopoverContent>
          </Popover>

          {/* Content Type Filter */}
          <Popover>
            <PopoverTrigger asChild>
              <div className="inline-flex items-center justify-between h-9 px-3 py-2 text-sm font-medium border border-input bg-background rounded-md cursor-pointer hover:bg-accent hover:text-accent-foreground">
                <div className="flex items-center gap-2">
                  <span>Tip sadržaja</span>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Info className="h-3 w-3 text-muted-foreground" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>
                        Post, Story, Reel, Video - odaberite format sadržaja
                      </p>
                    </TooltipContent>
                  </Tooltip>
                </div>
                <div className="flex items-center gap-2">
                  {filters.content_types?.length ? (
                    <Badge variant="secondary" className="text-xs">
                      {filters.content_types.length}
                    </Badge>
                  ) : null}
                  <ChevronDown className="h-4 w-4" />
                </div>
              </div>
            </PopoverTrigger>
            <PopoverContent className="w-64" align="start">
              <div className="space-y-2">
                <h4 className="font-medium text-sm">Tip sadržaja</h4>
                <p className="text-xs text-muted-foreground mb-3">
                  Kakav format sadržaja želite za vašu kampanju?
                </p>
                {contentTypes.map(contentType => (
                  <div
                    key={contentType.id}
                    className="flex items-center space-x-2"
                  >
                    <Checkbox
                      id={`content-${contentType.id}`}
                      checked={
                        filters.content_types?.includes(contentType.name) ||
                        false
                      }
                      onCheckedChange={() =>
                        toggleArrayFilter('content_types', contentType.name)
                      }
                    />
                    <label
                      htmlFor={`content-${contentType.id}`}
                      className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 flex items-center justify-between w-full"
                    >
                      <span>{contentType.name}</span>
                      <span className="text-xs text-muted-foreground">
                        {contentType.platforms.slice(0, 2).join(', ')}
                        {contentType.platforms.length > 2 && '...'}
                      </span>
                    </label>
                  </div>
                ))}
              </div>
            </PopoverContent>
          </Popover>

          {/* Category Filter */}
          <Popover>
            <PopoverTrigger asChild>
              <div className="inline-flex items-center justify-between h-9 px-3 py-2 text-sm font-medium border border-input bg-background rounded-md cursor-pointer hover:bg-accent hover:text-accent-foreground">
                <div className="flex items-center gap-2">
                  <span>Kategorija</span>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Info className="h-3 w-3 text-muted-foreground" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Odaberite niše koje odgovaraju vašoj kampanji</p>
                    </TooltipContent>
                  </Tooltip>
                </div>
                <div className="flex items-center gap-2">
                  {filters.categories?.length ? (
                    <Badge variant="secondary" className="text-xs">
                      {filters.categories.length}
                    </Badge>
                  ) : null}
                  <ChevronDown className="h-4 w-4" />
                </div>
              </div>
            </PopoverTrigger>
            <PopoverContent className="w-64" align="start">
              <div className="space-y-2">
                <h4 className="font-medium text-sm">Niša/Kategorija</h4>
                <p className="text-xs text-muted-foreground mb-3">
                  Odaberite kategorije koje najbolje opisuju vašu kampanju
                </p>
                {categories.map(category => (
                  <div
                    key={category.id}
                    className="flex items-center space-x-2"
                  >
                    <Checkbox
                      id={`category-${category.id}`}
                      checked={
                        filters.categories?.includes(category.name) || false
                      }
                      onCheckedChange={() =>
                        toggleArrayFilter('categories', category.name)
                      }
                    />
                    <label
                      htmlFor={`category-${category.id}`}
                      className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 flex items-center gap-2"
                    >
                      <span>{category.icon}</span>
                      {category.name}
                    </label>
                  </div>
                ))}
              </div>
            </PopoverContent>
          </Popover>

          {/* Price Range Filter */}
          <Popover>
            <PopoverTrigger asChild>
              <div className="inline-flex items-center justify-between h-9 px-3 py-2 text-sm font-medium border border-input bg-background rounded-md cursor-pointer hover:bg-accent hover:text-accent-foreground">
                <span>Cijena</span>
                <div className="flex items-center gap-2">
                  {filters.min_price || filters.max_price ? (
                    <Badge variant="secondary" className="text-xs">
                      {filters.min_price || 0}-{filters.max_price || 1000} KM
                    </Badge>
                  ) : null}
                  <ChevronDown className="h-4 w-4" />
                </div>
              </div>
            </PopoverTrigger>
            <PopoverContent className="w-80" align="start">
              <div className="space-y-4">
                <h4 className="font-medium text-sm">Budžet po objavi</h4>
                <div className="px-2">
                  <Slider
                    value={priceRange}
                    onValueChange={handlePriceChange}
                    max={1000}
                    min={0}
                    step={10}
                    className="w-full"
                  />
                  <div className="flex justify-between text-sm text-muted-foreground mt-2">
                    <span>{priceRange[0]} KM</span>
                    <span>{priceRange[1]} KM</span>
                  </div>
                </div>
              </div>
            </PopoverContent>
          </Popover>

          {/* Gender Filter */}
          <Select
            value={filters.gender || 'all'}
            onValueChange={value =>
              updateFilters('gender', value === 'all' ? undefined : value)
            }
          >
            <SelectTrigger>
              <SelectValue placeholder="Pol" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Svi</SelectItem>
              <SelectItem value="male">Muški</SelectItem>
              <SelectItem value="female">Ženski</SelectItem>
            </SelectContent>
          </Select>

          {/* Age Range Filter */}
          <Popover>
            <PopoverTrigger asChild>
              <div className="inline-flex items-center justify-between h-9 px-3 py-2 text-sm font-medium border border-input bg-background rounded-md cursor-pointer hover:bg-accent hover:text-accent-foreground">
                <span>Uzrast</span>
                <div className="flex items-center gap-2">
                  {filters.min_age || filters.max_age ? (
                    <Badge variant="secondary" className="text-xs">
                      {filters.min_age || 18}-{filters.max_age || 65}
                    </Badge>
                  ) : null}
                  <ChevronDown className="h-4 w-4" />
                </div>
              </div>
            </PopoverTrigger>
            <PopoverContent className="w-80" align="start">
              <div className="space-y-4">
                <h4 className="font-medium text-sm">Uzrast influencera</h4>
                <div className="px-2">
                  <Slider
                    value={ageRange}
                    onValueChange={handleAgeChange}
                    max={65}
                    min={18}
                    step={1}
                    className="w-full"
                  />
                  <div className="flex justify-between text-sm text-muted-foreground mt-2">
                    <span>{ageRange[0]} godina</span>
                    <span>{ageRange[1]} godina</span>
                  </div>
                </div>
              </div>
            </PopoverContent>
          </Popover>
        </div>

        {/* Active Filters */}
        {activeFiltersCount > 0 && (
          <div className="flex flex-wrap gap-2 mt-4 pt-4 border-t">
            {filters.platforms?.map(platform => (
              <Badge key={platform} variant="secondary" className="gap-1">
                {platforms.find(p => p.name === platform)?.icon} {platform}
                <X
                  className="h-3 w-3 cursor-pointer"
                  onClick={() => toggleArrayFilter('platforms', platform)}
                />
              </Badge>
            ))}
            {filters.content_types?.map(contentType => (
              <Badge key={contentType} variant="secondary" className="gap-1">
                {contentType}
                <X
                  className="h-3 w-3 cursor-pointer"
                  onClick={() =>
                    toggleArrayFilter('content_types', contentType)
                  }
                />
              </Badge>
            ))}
            {filters.categories?.map(category => (
              <Badge key={category} variant="secondary" className="gap-1">
                {categories.find(c => c.name === category)?.icon} {category}
                <X
                  className="h-3 w-3 cursor-pointer"
                  onClick={() => toggleArrayFilter('categories', category)}
                />
              </Badge>
            ))}
            {filters.gender && (
              <Badge variant="secondary" className="gap-1">
                {filters.gender === 'male' ? 'Muški' : 'Ženski'}
                <X
                  className="h-3 w-3 cursor-pointer"
                  onClick={() => updateFilters('gender', undefined)}
                />
              </Badge>
            )}
          </div>
        )}
      </div>
    </TooltipProvider>
  );
}
