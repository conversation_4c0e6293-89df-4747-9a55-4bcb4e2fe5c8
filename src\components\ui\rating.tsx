import React from 'react';
import { Star } from 'lucide-react';
import { cn } from '@/lib/utils';

interface RatingProps {
  rating: number;
  totalReviews?: number;
  showReviewCount?: boolean;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export function Rating({ 
  rating, 
  totalReviews = 0, 
  showReviewCount = true, 
  size = 'md',
  className 
}: RatingProps) {
  const sizeClasses = {
    sm: 'h-3 w-3',
    md: 'h-4 w-4',
    lg: 'h-5 w-5'
  };

  const textSizeClasses = {
    sm: 'text-xs',
    md: 'text-sm',
    lg: 'text-base'
  };

  const starSize = sizeClasses[size];
  const textSize = textSizeClasses[size];

  // Handle NaN, null, undefined values
  const safeRating = isNaN(rating) || rating == null ? 0 : rating;
  const safeTotalReviews = isNaN(totalReviews) || totalReviews == null ? 0 : totalReviews;

  // Round rating to 1 decimal place
  const displayRating = Math.round(safeRating * 10) / 10;

  // Generate stars
  const stars = [];
  for (let i = 1; i <= 5; i++) {
    const isFilled = i <= Math.floor(safeRating);
    const isHalfFilled = i === Math.ceil(safeRating) && safeRating % 1 !== 0 && i > Math.floor(safeRating);

    stars.push(
      <Star
        key={i}
        className={cn(
          starSize,
          isFilled
            ? 'fill-yellow-400 text-yellow-400'
            : isHalfFilled
            ? 'fill-yellow-400/50 text-yellow-400'
            : 'fill-gray-200 text-gray-200'
        )}
      />
    );
  }

  if (safeRating === 0 && safeTotalReviews === 0) {
    return (
      <div className={cn('flex items-center gap-1', className)}>
        <div className="flex">
          {[...Array(5)].map((_, i) => (
            <Star
              key={i}
              className={cn(starSize, 'fill-gray-200 text-gray-200')}
            />
          ))}
        </div>
        <span className={cn('text-muted-foreground', textSize)}>
          Nema ocjena
        </span>
      </div>
    );
  }

  return (
    <div className={cn('flex items-center gap-1', className)}>
      <div className="flex">
        {stars}
      </div>
      <span className={cn('font-medium', textSize)}>
        {displayRating}
      </span>
      {showReviewCount && safeTotalReviews > 0 && (
        <span className={cn('text-muted-foreground', textSize)}>
          ({safeTotalReviews})
        </span>
      )}
    </div>
  );
}

export default Rating;
