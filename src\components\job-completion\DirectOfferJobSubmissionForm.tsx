'use client';

import { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  Send,
  Upload,
  X,
  FileText,
  User,
  Calendar,
  DollarSign,
} from 'lucide-react';
import { submitDirectOfferJobCompletion } from '@/lib/job-completions';
import { toast } from 'sonner';

interface DirectOfferJobSubmissionFormProps {
  directOfferId: string;
  offerTitle?: string;
  businessName?: string;
  businessAvatar?: string | null;
  proposedRate?: number;
  onSuccess?: () => void;
  onCancel?: () => void;
}

export function DirectOfferJobSubmissionForm({
  directOfferId,
  offerTitle,
  businessName,
  businessAvatar,
  proposedRate,
  onSuccess,
  onCancel,
}: DirectOfferJobSubmissionFormProps) {
  const [submissionNotes, setSubmissionNotes] = useState('');
  const [files, setFiles] = useState<File[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      const newFiles = Array.from(e.target.files);
      setFiles(prev => [...prev, ...newFiles]);
    }
  };

  const removeFile = (index: number) => {
    setFiles(prev => prev.filter((_, i) => i !== index));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!submissionNotes.trim()) {
      toast.error('Please add submission notes');
      return;
    }

    setIsSubmitting(true);
    try {
      // For now, we'll just store file names in the submission_files JSON
      // In a real implementation, you'd upload files to storage first
      const fileData = files.map(file => ({
        name: file.name,
        size: file.size,
        type: file.type,
        // In real implementation, add: url: uploadedFileUrl
      }));

      const { error } = await submitDirectOfferJobCompletion(
        directOfferId,
        submissionNotes.trim(),
        fileData.length > 0 ? fileData : null
      );

      if (error) {
        toast.error('Neuspješno slanje završetka posla');
        return;
      }

      toast.success('Završetak posla je uspješno poslan');

      // Reset form
      setSubmissionNotes('');
      setFiles([]);

      onSuccess?.();
    } catch (error) {
      toast.error('Dogodila se neočekivana greška');
    } finally {
      setIsSubmitting(false);
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="text-lg">Pošaljite završetak posla</CardTitle>
        {offerTitle && (
          <p className="text-sm text-muted-foreground">Ponuda: {offerTitle}</p>
        )}
      </CardHeader>

      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Business Info */}
          {businessName && (
            <div className="space-y-2">
              <Label className="text-sm font-medium">Šaljete za</Label>
              <div className="flex items-center gap-3 p-3 bg-muted rounded-md">
                <Avatar className="h-10 w-10">
                  <AvatarImage src={businessAvatar || ''} />
                  <AvatarFallback>
                    <User className="h-5 w-5" />
                  </AvatarFallback>
                </Avatar>
                <div className="flex-1">
                  <p className="font-medium text-sm">{businessName}</p>
                  <p className="text-xs text-muted-foreground">Biznis</p>
                </div>
                {proposedRate && (
                  <div className="flex items-center gap-1 text-sm font-medium">
                    <DollarSign className="h-4 w-4" />${proposedRate}
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Submission Notes */}
          <div className="space-y-2">
            <Label htmlFor="submission-notes" className="text-sm font-medium">
              Napomene o završetku *
            </Label>
            <Textarea
              id="submission-notes"
              placeholder="Opišite šta ste završili, uključite linkove do objavljenog sadržaja i sve ostale relevantne detalje..."
              value={submissionNotes}
              onChange={e => setSubmissionNotes(e.target.value)}
              rows={6}
              className="resize-none"
            />
            <p className="text-xs text-muted-foreground">
              {submissionNotes.length}/1000 karaktera
            </p>
          </div>

          {/* File Upload */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">Prilozi (opcionalno)</Label>
            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <Input
                  type="file"
                  multiple
                  onChange={handleFileChange}
                  className="hidden"
                  id="file-upload"
                  accept="image/*,video/*,.pdf,.doc,.docx"
                />
                <Label
                  htmlFor="file-upload"
                  className="flex items-center gap-2 px-4 py-2 border border-dashed border-gray-300 rounded-md cursor-pointer hover:bg-gray-50"
                >
                  <Upload className="h-4 w-4" />
                  Učitajte fajlove
                </Label>
              </div>

              {files.length > 0 && (
                <div className="space-y-2">
                  {files.map((file, index) => (
                    <div
                      key={index}
                      className="flex items-center justify-between p-2 bg-muted rounded-md"
                    >
                      <div className="flex items-center gap-2">
                        <FileText className="h-4 w-4" />
                        <div>
                          <p className="text-sm font-medium">{file.name}</p>
                          <p className="text-xs text-muted-foreground">
                            {formatFileSize(file.size)}
                          </p>
                        </div>
                      </div>
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => removeFile(index)}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  ))}
                </div>
              )}
            </div>
            <p className="text-xs text-muted-foreground">
              Učitajte slike, videe ili dokumente vezane za vaš rad
            </p>
          </div>

          {/* Actions */}
          <div className="flex gap-3 pt-4">
            <Button
              type="submit"
              disabled={isSubmitting || !submissionNotes.trim()}
              className="flex-1"
            >
              <Send className="h-4 w-4 mr-2" />
              {isSubmitting ? 'Šalje se...' : 'Pošaljite na pregled'}
            </Button>
            {onCancel && (
              <Button
                type="button"
                variant="outline"
                onClick={onCancel}
                disabled={isSubmitting}
              >
                Otkaži
              </Button>
            )}
          </div>
        </form>
      </CardContent>
    </Card>
  );
}
