-- Add country field to profiles table
-- This migration adds a country field to store user's country separately from location (city)

-- 1. Add country column to profiles table
ALTER TABLE profiles 
ADD COLUMN IF NOT EXISTS country VARCHAR(50);

-- 2. Add city column to profiles table (to replace location for city-specific data)
ALTER TABLE profiles 
ADD COLUMN IF NOT EXISTS city VARCHAR(100);

-- 3. Add profile_completed column to track onboarding completion
ALTER TABLE profiles 
ADD COLUMN IF NOT EXISTS profile_completed BOOLEAN DEFAULT FALSE;

-- 4. Create index for country field for better performance
CREATE INDEX IF NOT EXISTS idx_profiles_country ON profiles(country);

-- 5. Create index for profile_completed field
CREATE INDEX IF NOT EXISTS idx_profiles_completed ON profiles(profile_completed);

-- 6. Update RLS policies to include new fields (if needed)
-- The existing policies should automatically cover new fields

-- 7. Add comment for documentation
COMMENT ON COLUMN profiles.country IS 'User country (e.g., bosnia-herzegovina, serbia, croatia)';
COMMENT ON COLUMN profiles.city IS 'User city (e.g., Sarajevo, Belgrade, Zagreb)';
COMMENT ON COLUMN profiles.profile_completed IS 'Whether user has completed the onboarding process';
