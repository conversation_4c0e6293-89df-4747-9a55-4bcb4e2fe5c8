import React from 'react';
import { cn } from '@/lib/utils';
import { Button, ButtonProps } from '@/components/ui/button';

interface GradientButtonProps extends ButtonProps {
  gradientVariant?: 'instagram' | 'white' | 'glass';
}

const GradientButton = React.forwardRef<HTMLButtonElement, GradientButtonProps>(
  ({ className, gradientVariant = 'instagram', ...props }, ref) => {
    const gradientClasses = {
      instagram: 'bg-instagram-primary text-white hover:bg-instagram-secondary shadow-lg hover:shadow-xl transition-all duration-300',
      white: 'bg-white text-instagram-purple hover:bg-white/90 font-semibold shadow-lg hover:shadow-xl transition-all duration-300',
      glass: 'bg-white/10 border-white/30 text-white hover:bg-white/20 backdrop-blur-sm shadow-lg hover:shadow-xl transition-all duration-300'
    };

    return (
      <Button
        ref={ref}
        className={cn(
          gradientClasses[gradientVariant],
          className
        )}
        {...props}
      />
    );
  }
);

GradientButton.displayName = 'GradientButton';

export { GradientButton };
