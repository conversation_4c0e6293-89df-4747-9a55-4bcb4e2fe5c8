'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { UsernameStep } from '@/components/onboarding/steps/UsernameStep';
import { CategoriesStep } from '@/components/onboarding/steps/CategoriesStep';
import { CountryStep } from '@/components/onboarding/steps/CountryStep';
import { BusinessNameStep } from '@/components/onboarding/steps/BusinessNameStep';
import { BusinessBioStep } from '@/components/onboarding/steps/BusinessBioStep';
import { BusinessSocialMediaStep } from '@/components/onboarding/steps/BusinessSocialMediaStep';
import { CityStep } from '@/components/onboarding/steps/CityStep';
import { toast } from 'sonner';
import { supabase } from '@/lib/supabase';

interface OnboardingData {
  username: string;
  categories: number[];
  country: string;
  businessName: string;
  bio: string;
  socialMedia: {
    instagram_handle?: string;
    instagram_followers?: number;
    tiktok_handle?: string;
    tiktok_followers?: number;
    youtube_handle?: string;
    youtube_subscribers?: number;
  };
  city: string;
}

const TOTAL_STEPS = 7;

const ONBOARDING_STEPS = [
  { id: 1, title: 'Username', description: 'Odaberite korisničko ime' },
  { id: 2, title: 'Kategorije', description: 'Odaberite djelatnost/branšu' },
  { id: 3, title: 'Država', description: 'Odaberite vašu državu' },
  { id: 4, title: 'Naziv firme', description: 'Unesite naziv vašeg brenda' },
  { id: 5, title: 'Opis firme', description: 'Opišite vašu firmu' },
  { id: 6, title: 'Društvene mreže', description: 'Dodajte vaše profile' },
  { id: 7, title: 'Lokacija', description: 'Unesite vaš grad' },
];

export default function BusinessOnboardingPage() {
  const router = useRouter();
  const { user } = useAuth();
  const [currentStep, setCurrentStep] = useState(1);
  const [isLoading, setIsLoading] = useState(false);
  const [onboardingData, setOnboardingData] = useState<OnboardingData>({
    username: '',
    categories: [],
    country: '',
    businessName: '',
    bio: '',
    socialMedia: {},
    city: '',
  });

  useEffect(() => {
    if (!user) {
      router.push('/prijava');
      return;
    }
  }, [user, router]);

  const updateData = (stepData: Partial<OnboardingData>) => {
    setOnboardingData(prev => ({ ...prev, ...stepData }));
  };

  const nextStep = () => {
    if (currentStep < TOTAL_STEPS) {
      setCurrentStep(prev => prev + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(prev => prev - 1);
    }
  };

  const completeOnboarding = async () => {
    if (!user) return;

    setIsLoading(true);
    try {
      // Update profile
      const { error: profileError } = await supabase
        .from('profiles')
        .update({
          username: onboardingData.username,
          full_name: onboardingData.businessName,
          bio: onboardingData.bio || null,
          country: onboardingData.country,
          city: onboardingData.city || null,
          user_type: 'business',
          profile_completed: true,
        })
        .eq('id', user.id);

      if (profileError) throw profileError;

      // Create business record
      const { error: businessError } = await supabase
        .from('businesses')
        .insert({
          id: user.id,
          company_name: onboardingData.businessName,
          industry: onboardingData.categories[0] || null, // Use first category as industry
        });

      if (businessError) throw businessError;

      // Handle categories
      if (onboardingData.categories.length > 0) {
        const categoryInserts = onboardingData.categories.map(categoryId => ({
          business_id: user.id,
          category_id: categoryId,
        }));

        const { error: categoriesError } = await supabase
          .from('business_target_categories')
          .insert(categoryInserts);

        if (categoriesError) throw categoriesError;
      }

      // Handle social media platforms
      const platformInserts = [];
      if (onboardingData.socialMedia.instagram_handle) {
        platformInserts.push({
          business_id: user.id,
          platform_id: 1, // Instagram
          handle: onboardingData.socialMedia.instagram_handle,
          followers_count: onboardingData.socialMedia.instagram_followers || 0,
        });
      }
      if (onboardingData.socialMedia.tiktok_handle) {
        platformInserts.push({
          business_id: user.id,
          platform_id: 2, // TikTok
          handle: onboardingData.socialMedia.tiktok_handle,
          followers_count: onboardingData.socialMedia.tiktok_followers || 0,
        });
      }
      if (onboardingData.socialMedia.youtube_handle) {
        platformInserts.push({
          business_id: user.id,
          platform_id: 3, // YouTube
          handle: onboardingData.socialMedia.youtube_handle,
          followers_count: onboardingData.socialMedia.youtube_subscribers || 0,
        });
      }

      if (platformInserts.length > 0) {
        const { error: platformsError } = await supabase
          .from('business_platforms')
          .insert(platformInserts);

        if (platformsError) throw platformsError;
      }

      toast.success('Profil je uspešno kreiran!');
      router.push('/dashboard/biznis');
    } catch (error) {
      console.error('Error completing onboarding:', error);
      toast.error('Greška pri kreiranju profila');
    } finally {
      setIsLoading(false);
    }
  };

  const renderStep = () => {
    switch (currentStep) {
      case 1:
        return (
          <UsernameStep
            value={onboardingData.username}
            onChange={(username) => updateData({ username })}
            onNext={nextStep}
            title="Korisničko ime"
            description="Ovo korisničko ime će biti vidljivo influencerima na platformi"
          />
        );
      case 2:
        return (
          <CategoriesStep
            value={onboardingData.categories}
            onChange={(categories) => updateData({ categories })}
            onNext={nextStep}
            onBack={prevStep}
            title="Odaberite djelatnost/branšu"
            description="Koje kategorije najbolje opisuju industriju u kojoj posluje vaš brend?"
            maxCategories={3}
          />
        );
      case 3:
        return (
          <CountryStep
            value={onboardingData.country}
            onChange={(country) => updateData({ country })}
            onNext={nextStep}
            onBack={prevStep}
            title="Iz koje ste države?"
            description="Ova informacija pomaže influencerima da pronađu lokalne brendove"
          />
        );
      case 4:
        return (
          <BusinessNameStep
            value={onboardingData.businessName}
            onNext={(businessName) => {
              updateData({ businessName });
              nextStep();
            }}
            onBack={prevStep}
          />
        );
      case 5:
        return (
          <BusinessBioStep
            value={onboardingData.bio}
            onNext={(bio) => {
              updateData({ bio });
              nextStep();
            }}
            onBack={prevStep}
          />
        );
      case 6:
        return (
          <BusinessSocialMediaStep
            value={onboardingData.socialMedia}
            onNext={(socialMedia) => {
              updateData({ socialMedia });
              nextStep();
            }}
            onBack={prevStep}
          />
        );
      case 7:
        return (
          <CityStep
            value={onboardingData.city}
            onChange={(city) => updateData({ city })}
            onNext={(city) => {
              updateData({ city });
              completeOnboarding();
            }}
            onBack={prevStep}
            isLastStep={true}
            isLoading={isLoading}
            title="U kom gradu se nalazi vaša firma?"
            description="Ovo polje je opcionalno, ali pomaže influencerima da pronađu lokalne brendove"
          />
        );
      default:
        return null;
    }
  };

  const renderCurrentStep = () => {
    return renderStep();
  };

  if (!user) {
    return null;
  }

  const progressPercentage = ((currentStep - 1) / (TOTAL_STEPS - 1)) * 100;

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 py-8">
      <div className="container mx-auto px-4 max-w-2xl">
        {/* Progress bar */}
        <div className="mb-8">
          <div className="flex justify-between items-center mb-2">
            <span className="text-sm font-medium text-gray-700">
              Korak {currentStep} od {TOTAL_STEPS}
            </span>
            <span className="text-sm text-gray-500">
              {Math.round(progressPercentage)}%
            </span>
          </div>
          <Progress value={progressPercentage} className="h-2" />
        </div>

        {/* Main content */}
        <Card className="shadow-lg">
          <CardHeader className="text-center">
            <CardTitle className="text-2xl text-gray-900">
              Kreiranje profila - Biznis
            </CardTitle>
            <p className="text-gray-600 mt-2">
              {ONBOARDING_STEPS[currentStep - 1]?.description}
            </p>
          </CardHeader>
          <CardContent>
            {renderCurrentStep()}
          </CardContent>
        </Card>

        {/* Step indicator */}
        <div className="mt-6 text-center text-sm text-gray-500">
          {ONBOARDING_STEPS[currentStep - 1]?.title}
        </div>
      </div>
    </div>
  );
}
