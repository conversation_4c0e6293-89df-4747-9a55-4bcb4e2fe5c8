'use client';

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Plus, X, Loader2, Package } from 'lucide-react';
import { cn } from '@/lib/utils';
import {
  getPlatforms,
  getContentTypes,
  generatePackageName,
  VIDEO_DURATIONS,
  type Platform,
  type ContentType,
} from '@/lib/pricing-packages';

const packageSchema = z.object({
  platform_id: z.number().min(1, 'Izaberite platformu'),
  content_type_id: z.number().min(1, 'Izaberite tip sadržaja'),
  quantity: z.number().min(1, 'Količina mora biti najmanje 1'),
  video_duration: z.string().optional().or(z.literal('')),
  price: z.number().min(0.01, 'Cijena mora biti veća od 0'),
});

type PackageForm = z.infer<typeof packageSchema>;

interface PackageData {
  platform_id: number;
  content_type_id: number;
  quantity: number;
  video_duration?: string;
  price: number;
  // For display
  platform_name: string;
  platform_icon: string;
  content_type_name: string;
  generated_name: string;
}

interface PackageCreationStepProps {
  onNext: (data: { packages: PackageData[] }) => void;
  initialData?: { packages: PackageData[] };
}

export function PackageCreationStep({ onNext, initialData }: PackageCreationStepProps) {
  const [platforms, setPlatforms] = useState<Platform[]>([]);
  const [contentTypes, setContentTypes] = useState<ContentType[]>([]);
  const [packages, setPackages] = useState<PackageData[]>(initialData?.packages || []);
  const [loading, setLoading] = useState(true);
  const [showForm, setShowForm] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    setValue,
    watch,
  } = useForm<PackageForm>({
    resolver: zodResolver(packageSchema),
  });

  const watchedPlatformId = watch('platform_id');
  const watchedContentTypeId = watch('content_type_id');
  const watchedQuantity = watch('quantity');
  const watchedVideoDuration = watch('video_duration');

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      const [platformsResult, contentTypesResult] = await Promise.all([
        getPlatforms(),
        getContentTypes(),
      ]);

      if (platformsResult.error) {
        console.error('Error loading platforms:', platformsResult.error);
      } else {
        setPlatforms(platformsResult.data || []);
      }

      if (contentTypesResult.error) {
        console.error('Error loading content types:', contentTypesResult.error);
      } else {
        setContentTypes(contentTypesResult.data || []);
      }
    } catch (error) {
      console.error('Error loading data:', error);
    } finally {
      setLoading(false);
    }
  };

  const getFilteredContentTypes = () => {
    if (!watchedPlatformId) return [];
    return contentTypes.filter(ct => ct.platform_id === watchedPlatformId);
  };

  const getSelectedPlatform = () => {
    return platforms.find(p => p.id === watchedPlatformId);
  };

  const getSelectedContentType = () => {
    return contentTypes.find(ct => ct.id === watchedContentTypeId);
  };

  const getPreviewPackageName = () => {
    const platform = getSelectedPlatform();
    const contentType = getSelectedContentType();
    
    if (!platform || !contentType || !watchedQuantity) return '';

    const isVideoContent = contentType.name.toLowerCase().includes('video');
    const duration = isVideoContent && watchedVideoDuration ? watchedVideoDuration : undefined;

    return generatePackageName(
      watchedQuantity,
      platform.name,
      contentType.name,
      duration
    );
  };

  const onSubmit = (data: PackageForm) => {
    const platform = getSelectedPlatform();
    const contentType = getSelectedContentType();
    
    if (!platform || !contentType) return;

    const isVideoContent = contentType.name.toLowerCase().includes('video');
    const cleanVideoDuration = isVideoContent && data.video_duration ? data.video_duration : undefined;

    const newPackage: PackageData = {
      platform_id: data.platform_id,
      content_type_id: data.content_type_id,
      quantity: data.quantity,
      video_duration: cleanVideoDuration,
      price: data.price,
      platform_name: platform.name,
      platform_icon: platform.icon,
      content_type_name: contentType.name,
      generated_name: generatePackageName(
        data.quantity,
        platform.name,
        contentType.name,
        cleanVideoDuration
      ),
    };

    setPackages(prev => [...prev, newPackage]);
    setShowForm(false);
    reset();
  };

  const handleRemovePackage = (index: number) => {
    setPackages(prev => prev.filter((_, i) => i !== index));
  };

  const handleNext = () => {
    onNext({ packages });
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Created packages */}
      {packages.length > 0 && (
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Krerani paketi</h3>
          <div className="space-y-3">
            {packages.map((pkg, index) => (
              <Card key={index}>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 rounded-lg bg-blue-100 flex items-center justify-center">
                        <span className="text-xl">{pkg.platform_icon}</span>
                      </div>
                      <div>
                        <p className="font-medium">{pkg.generated_name}</p>
                        <p className="text-sm text-gray-600">
                          {pkg.price.toFixed(2)} KM
                        </p>
                      </div>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleRemovePackage(index)}
                      className="text-red-600 hover:text-red-700 hover:bg-red-50"
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      )}

      {/* Package creation form */}
      {showForm && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Package className="h-5 w-5" />
              <span>Kreiraj novi paket</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Platform */}
                <div className="space-y-2">
                  <Label>Platforma *</Label>
                  <Select onValueChange={(value) => setValue('platform_id', parseInt(value))}>
                    <SelectTrigger>
                      <SelectValue placeholder="Izaberite platformu" />
                    </SelectTrigger>
                    <SelectContent>
                      {platforms.map((platform) => (
                        <SelectItem key={platform.id} value={platform.id.toString()}>
                          <div className="flex items-center space-x-2">
                            <span>{platform.icon}</span>
                            <span>{platform.name}</span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {errors.platform_id && (
                    <p className="text-sm text-red-500">{errors.platform_id.message}</p>
                  )}
                </div>

                {/* Content Type */}
                <div className="space-y-2">
                  <Label>Tip sadržaja *</Label>
                  <Select 
                    onValueChange={(value) => setValue('content_type_id', parseInt(value))}
                    disabled={!watchedPlatformId}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Izaberite tip sadržaja" />
                    </SelectTrigger>
                    <SelectContent>
                      {getFilteredContentTypes().map((contentType) => (
                        <SelectItem key={contentType.id} value={contentType.id.toString()}>
                          {contentType.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {errors.content_type_id && (
                    <p className="text-sm text-red-500">{errors.content_type_id.message}</p>
                  )}
                </div>

                {/* Quantity */}
                <div className="space-y-2">
                  <Label htmlFor="quantity">Količina *</Label>
                  <Input
                    id="quantity"
                    type="number"
                    min="1"
                    placeholder="1"
                    {...register('quantity', { valueAsNumber: true })}
                  />
                  {errors.quantity && (
                    <p className="text-sm text-red-500">{errors.quantity.message}</p>
                  )}
                </div>

                {/* Video Duration */}
                {getSelectedContentType()?.name.toLowerCase().includes('video') && (
                  <div className="space-y-2">
                    <Label>Trajanje videa</Label>
                    <Select onValueChange={(value) => setValue('video_duration', value)}>
                      <SelectTrigger>
                        <SelectValue placeholder="Izaberite trajanje" />
                      </SelectTrigger>
                      <SelectContent>
                        {VIDEO_DURATIONS.map((duration) => (
                          <SelectItem key={duration.value} value={duration.value}>
                            {duration.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                )}

                {/* Price */}
                <div className="space-y-2">
                  <Label htmlFor="price">Cijena (KM) *</Label>
                  <Input
                    id="price"
                    type="number"
                    step="0.01"
                    min="0.01"
                    placeholder="100.00"
                    {...register('price', { valueAsNumber: true })}
                  />
                  {errors.price && (
                    <p className="text-sm text-red-500">{errors.price.message}</p>
                  )}
                </div>
              </div>

              {/* Package Preview */}
              {getPreviewPackageName() && (
                <div className="p-3 bg-gray-50 rounded-lg">
                  <p className="text-sm text-gray-600">Naziv paketa:</p>
                  <p className="font-medium">{getPreviewPackageName()}</p>
                </div>
              )}

              <div className="flex space-x-2">
                <Button type="submit">Dodaj paket</Button>
                <Button type="button" variant="outline" onClick={() => setShowForm(false)}>
                  Otkaži
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      )}

      {/* Add package button */}
      {!showForm && (
        <div className="text-center">
          <Button onClick={() => setShowForm(true)} variant="outline" className="w-full">
            <Plus className="h-4 w-4 mr-2" />
            Dodaj paket
          </Button>
        </div>
      )}

      {/* Continue button */}
      <div className="space-y-4">
        {packages.length === 0 && (
          <div className="text-center py-8">
            <Package className="h-12 w-12 mx-auto text-gray-400 mb-4" />
            <p className="text-gray-600 mb-2">
              Morate kreirati najmanje jedan paket da biste završili registraciju.
            </p>
            <p className="text-sm text-gray-500">
              Paketi definišu usluge koje nudite i njihove cijene.
            </p>
          </div>
        )}
        
        <div className="flex justify-center">
          <Button 
            onClick={handleNext} 
            disabled={packages.length === 0}
            className="min-w-32"
          >
            Završi registraciju
          </Button>
        </div>
      </div>
    </div>
  );
}
