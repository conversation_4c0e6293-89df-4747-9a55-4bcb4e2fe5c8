'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { ArrowLeft, User, Building2 } from 'lucide-react';

export default function RegistracijaPage() {
  const router = useRouter();
  const [selectedType, setSelectedType] = useState<
    'influencer' | 'business' | null
  >(null);

  const handleContinue = () => {
    if (selectedType) {
      router.push(`/registracija/${selectedType}`);
    }
  };

  return (
    <div className="min-h-screen bg-instagram-warm relative overflow-hidden flex flex-col">
      {/* Background decorative elements */}
      <div className="absolute inset-0 bg-gradient-to-br from-white/5 via-transparent to-black/5"></div>
      <div className="absolute top-20 left-20 w-64 h-64 bg-white/10 rounded-full blur-3xl"></div>
      <div className="absolute bottom-20 right-20 w-80 h-80 bg-white/10 rounded-full blur-3xl"></div>

      {/* Header */}
      <header className="relative z-10 border-b border-white/20 backdrop-blur-sm bg-white/10">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <Link
            href="/"
            className="flex items-center space-x-2 text-white/80 hover:text-white transition-colors"
          >
            <ArrowLeft className="h-4 w-4" />
            <span>Nazad</span>
          </Link>
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-white/20 backdrop-blur-sm rounded-lg flex items-center justify-center border border-white/30">
              <span className="text-white font-bold text-lg">
                🔗
              </span>
            </div>
            <span className="text-xl font-bold text-white">
              InfluConnect
            </span>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="relative z-10 flex-1 flex items-center justify-center px-4 py-12">
        <div className="w-full max-w-md space-y-8">
          <div className="text-center">
            <h1 className="text-3xl font-bold text-white mb-2">
              Registracija
            </h1>
            <p className="text-white/70">
              Izaberite tip korisnika da biste nastavili
            </p>
          </div>

          <div className="space-y-4">
            {/* Influencer Card */}
            <Card
              className={`cursor-pointer transition-all hover:shadow-lg glass-instagram border-white/20 ${
                selectedType === 'influencer'
                  ? 'ring-2 ring-white border-white shadow-xl'
                  : 'hover:border-white/40'
              }`}
              onClick={() => setSelectedType('influencer')}
            >
              <CardHeader className="text-center">
                <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-4 backdrop-blur-sm">
                  <User className="h-8 w-8 text-white" />
                </div>
                <CardTitle className="text-xl text-white">Influencer</CardTitle>
                <CardDescription className="text-white/70">
                  Zarađujem kroz kreiranje sadržaja i promociju brendova
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="text-sm text-white/80 space-y-2">
                  <li className="flex items-center space-x-2">
                    <span className="text-green-300">✓</span>
                    <span>Pronađi kampanje koje odgovaraju tvojoj niši</span>
                  </li>
                  <li className="flex items-center space-x-2">
                    <span className="text-green-300">✓</span>
                    <span>Postavi svoje cijene i uslove</span>
                  </li>
                  <li className="flex items-center space-x-2">
                    <span className="text-green-300">✓</span>
                    <span>Sigurno plaćanje kroz escrow sistem</span>
                  </li>
                </ul>
              </CardContent>
            </Card>

            {/* Business Card */}
            <Card
              className={`cursor-pointer transition-all hover:shadow-lg glass-instagram border-white/20 ${
                selectedType === 'business'
                  ? 'ring-2 ring-white border-white shadow-xl'
                  : 'hover:border-white/40'
              }`}
              onClick={() => setSelectedType('business')}
            >
              <CardHeader className="text-center">
                <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-4 backdrop-blur-sm">
                  <Building2 className="h-8 w-8 text-white" />
                </div>
                <CardTitle className="text-xl text-white">Biznis</CardTitle>
                <CardDescription className="text-white/70">
                  Kreiram kampanje za promociju svojih proizvoda ili usluga
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="text-sm text-white/80 space-y-2">
                  <li className="flex items-center space-x-2">
                    <span className="text-green-300">✓</span>
                    <span>Kreiraj kampanje za svaki budžet</span>
                  </li>
                  <li className="flex items-center space-x-2">
                    <span className="text-green-300">✓</span>
                    <span>Biraj između stotina lokalnih influencera</span>
                  </li>
                  <li className="flex items-center space-x-2">
                    <span className="text-green-300">✓</span>
                    <span>Transparentno praćenje rezultata</span>
                  </li>
                </ul>
              </CardContent>
            </Card>
          </div>

          {/* Continue Button */}
          <Button
            onClick={handleContinue}
            disabled={!selectedType}
            className="w-full bg-white text-instagram-purple hover:bg-white/90 font-semibold shadow-lg hover:shadow-xl transition-all duration-300 disabled:bg-white/50 disabled:text-instagram-purple/50"
            size="lg"
          >
            Nastavi →
          </Button>

          {/* Login Link */}
          <div className="text-center">
            <p className="text-sm text-white/70">
              Već imate nalog?{' '}
              <Link
                href="/prijava"
                className="text-white hover:underline font-medium transition-colors"
              >
                Prijavite se
              </Link>
            </p>
          </div>
        </div>
      </main>
    </div>
  );
}
