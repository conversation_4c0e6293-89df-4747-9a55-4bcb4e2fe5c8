'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { Calendar } from '@/components/ui/calendar';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { CalendarIcon, DollarSign, Send, X } from 'lucide-react';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';
import { useAuth } from '@/contexts/AuthContext';
import { createDirectOffer } from '@/lib/offers';
import { toast } from 'sonner';

// Schema za validaciju
const offerSchema = z.object({
  title: z
    .string()
    .min(5, 'Naslov mora imati najmanje 5 karaktera')
    .max(200, 'Naslov je predugačak'),
  description: z.string().min(20, 'Opis mora imati najmanje 20 karaktera'),
  budget: z
    .number()
    .min(50, 'Minimalni budžet je 50 KM')
    .max(50000, 'Maksimalni budžet je 50,000 KM'),
  contentTypes: z
    .array(z.string())
    .min(1, 'Morate odabrati najmanje jedan tip sadržaja'),
  platforms: z
    .array(z.string())
    .min(1, 'Morate odabrati najmanje jednu platformu'),
  deadline: z.date().optional(),
  requirements: z.string().optional(),
  deliverables: z.string().optional(),
  businessMessage: z
    .string()
    .min(10, 'Poruka mora imati najmanje 10 karaktera'),
});

type OfferForm = z.infer<typeof offerSchema>;

interface DirectOfferFormProps {
  influencerId: string;
  influencerName: string;
  onSuccess: () => void;
  onCancel: () => void;
}

const contentTypeOptions = [
  { id: 'post', label: 'Instagram Post' },
  { id: 'story', label: 'Instagram Story' },
  { id: 'reel', label: 'Instagram Reel' },
  { id: 'video', label: 'TikTok Video' },
  { id: 'youtube_video', label: 'YouTube Video' },
  { id: 'youtube_short', label: 'YouTube Short' },
];

const platformOptions = [
  { id: 'instagram', label: 'Instagram' },
  { id: 'tiktok', label: 'TikTok' },
  { id: 'youtube', label: 'YouTube' },
  { id: 'facebook', label: 'Facebook' },
  { id: 'twitter', label: 'Twitter/X' },
];

export function DirectOfferForm({
  influencerId,
  influencerName,
  onSuccess,
  onCancel,
}: DirectOfferFormProps) {
  const { user } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const [selectedContentTypes, setSelectedContentTypes] = useState<string[]>(
    []
  );
  const [selectedPlatforms, setSelectedPlatforms] = useState<string[]>([]);

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
    setError,
  } = useForm<OfferForm>({
    resolver: zodResolver(offerSchema),
    defaultValues: {
      contentTypes: [],
      platforms: [],
    },
  });

  const watchedValues = watch();

  const handleContentTypeChange = (contentTypeId: string, checked: boolean) => {
    const newSelection = checked
      ? [...selectedContentTypes, contentTypeId]
      : selectedContentTypes.filter(id => id !== contentTypeId);

    setSelectedContentTypes(newSelection);
    setValue('contentTypes', newSelection);
  };

  const handlePlatformChange = (platformId: string, checked: boolean) => {
    const newSelection = checked
      ? [...selectedPlatforms, platformId]
      : selectedPlatforms.filter(id => id !== platformId);

    setSelectedPlatforms(newSelection);
    setValue('platforms', newSelection);
  };

  const onSubmit = async (data: OfferForm) => {
    if (!user) return;

    setIsLoading(true);

    try {
      const offerData = {
        influencer_id: influencerId,
        title: data.title,
        description: data.description,
        budget: data.budget,
        content_types: data.contentTypes,
        platforms: data.platforms,
        deadline: data.deadline ? format(data.deadline, 'yyyy-MM-dd') : null,
        requirements: data.requirements || null,
        // Note: deliverables and business_message fields don't exist in direct_offers table
      };

      const { error } = await createDirectOffer(offerData);

      if (error) {
        setError('root', { message: 'Greška pri slanju ponude' });
        return;
      }

      toast.success('Ponuda je uspješno poslana!');
      onSuccess();
    } catch (error) {
      setError('root', { message: 'Neočekivana greška' });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center">
        <h2 className="text-2xl font-bold">Pošaljite direktnu ponudu</h2>
        <p className="text-muted-foreground mt-1">
          Pošaljite ponudu za saradnju sa{' '}
          <span className="font-medium">{influencerName}</span>
        </p>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        {/* Osnovne informacije */}
        <Card>
          <CardHeader>
            <CardTitle>Osnovne informacije</CardTitle>
            <CardDescription>
              Unesite osnovne detalje vaše ponude
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Naslov */}
            <div>
              <Label htmlFor="title">Naslov ponude *</Label>
              <Input
                id="title"
                {...register('title')}
                placeholder="npr. Promocija novog proizvoda"
              />
              {errors.title && (
                <p className="text-sm text-destructive mt-1">
                  {errors.title.message}
                </p>
              )}
            </div>

            {/* Opis */}
            <div>
              <Label htmlFor="description">Opis projekta *</Label>
              <Textarea
                id="description"
                {...register('description')}
                placeholder="Opišite detalje vašeg projekta i šta očekujete od influencera..."
                rows={4}
              />
              {errors.description && (
                <p className="text-sm text-destructive mt-1">
                  {errors.description.message}
                </p>
              )}
            </div>

            {/* Budžet */}
            <div>
              <Label htmlFor="budget">Budžet (KM) *</Label>
              <div className="relative">
                <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  id="budget"
                  type="number"
                  {...register('budget', { valueAsNumber: true })}
                  placeholder="1000"
                  className="pl-10"
                />
              </div>
              {errors.budget && (
                <p className="text-sm text-destructive mt-1">
                  {errors.budget.message}
                </p>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Tip sadržaja */}
        <Card>
          <CardHeader>
            <CardTitle>Tip sadržaja *</CardTitle>
            <CardDescription>
              Odaberite tipove sadržaja koje želite
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
              {contentTypeOptions.map(option => (
                <div key={option.id} className="flex items-center space-x-2">
                  <Checkbox
                    id={`content-${option.id}`}
                    checked={selectedContentTypes.includes(option.id)}
                    onCheckedChange={checked =>
                      handleContentTypeChange(option.id, !!checked)
                    }
                  />
                  <Label htmlFor={`content-${option.id}`} className="text-sm">
                    {option.label}
                  </Label>
                </div>
              ))}
            </div>
            {errors.contentTypes && (
              <p className="text-sm text-destructive mt-2">
                {errors.contentTypes.message}
              </p>
            )}
          </CardContent>
        </Card>

        {/* Platforme */}
        <Card>
          <CardHeader>
            <CardTitle>Platforme *</CardTitle>
            <CardDescription>
              Odaberite platforme na kojima želite promociju
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
              {platformOptions.map(option => (
                <div key={option.id} className="flex items-center space-x-2">
                  <Checkbox
                    id={`platform-${option.id}`}
                    checked={selectedPlatforms.includes(option.id)}
                    onCheckedChange={checked =>
                      handlePlatformChange(option.id, !!checked)
                    }
                  />
                  <Label htmlFor={`platform-${option.id}`} className="text-sm">
                    {option.label}
                  </Label>
                </div>
              ))}
            </div>
            {errors.platforms && (
              <p className="text-sm text-destructive mt-2">
                {errors.platforms.message}
              </p>
            )}
          </CardContent>
        </Card>

        {/* Dodatne informacije */}
        <Card>
          <CardHeader>
            <CardTitle>Dodatne informacije</CardTitle>
            <CardDescription>Opcionalne informacije o projektu</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Deadline */}
            <div>
              <Label>Rok za završetak</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      'w-full justify-start text-left font-normal',
                      !watchedValues.deadline && 'text-muted-foreground'
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {watchedValues.deadline
                      ? format(watchedValues.deadline, 'dd.MM.yyyy')
                      : 'Odaberite datum'}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={watchedValues.deadline}
                    onSelect={date => setValue('deadline', date)}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>

            {/* Zahtjevi */}
            <div>
              <Label htmlFor="requirements">Specifični zahtjevi</Label>
              <Textarea
                id="requirements"
                {...register('requirements')}
                placeholder="Specifični zahtjevi za sadržaj, hashtag-ovi, mention-i..."
                rows={3}
              />
            </div>

            {/* Deliverables */}
            <div>
              <Label htmlFor="deliverables">Očekivani rezultati</Label>
              <Textarea
                id="deliverables"
                {...register('deliverables')}
                placeholder="Šta očekujete da dobijete (broj postova, video, slike...)?"
                rows={3}
              />
            </div>

            {/* Poruka */}
            <div>
              <Label htmlFor="businessMessage">Vaša poruka *</Label>
              <Textarea
                id="businessMessage"
                {...register('businessMessage')}
                placeholder="Predstavite se i objasnite zašto želite saradnju sa ovim influencerom..."
                rows={4}
              />
              {errors.businessMessage && (
                <p className="text-sm text-destructive mt-1">
                  {errors.businessMessage.message}
                </p>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Error message */}
        {errors.root && (
          <div className="text-sm text-destructive text-center">
            {errors.root.message}
          </div>
        )}

        {/* Action buttons */}
        <div className="flex justify-between">
          <Button type="button" variant="outline" onClick={onCancel}>
            Otkaži
          </Button>
          <Button type="submit" disabled={isLoading}>
            {isLoading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Šalje se...
              </>
            ) : (
              <>
                <Send className="mr-2 h-4 w-4" />
                Pošalji ponudu
              </>
            )}
          </Button>
        </div>
      </form>
    </div>
  );
}
