'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Plus, X, Instagram, Youtube } from 'lucide-react';
import { cn } from '@/lib/utils';

// TikTok icon component since it's not in Lucide
const TikTokIcon = ({ className }: { className?: string }) => (
  <svg
    className={className}
    viewBox="0 0 24 24"
    fill="currentColor"
  >
    <path d="M19.59 6.69a4.83 4.83 0 0 1-3.77-4.25V2h-3.45v13.67a2.89 2.89 0 0 1-5.2 1.74 2.89 2.89 0 0 1 2.31-4.64 2.93 2.93 0 0 1 .88.13V9.4a6.84 6.84 0 0 0-1-.05A6.33 6.33 0 0 0 5 20.1a6.34 6.34 0 0 0 10.86-4.43v-7a8.16 8.16 0 0 0 4.77 1.52v-3.4a4.85 4.85 0 0 1-1-.1z"/>
  </svg>
);

interface SocialPlatform {
  id: 'instagram' | 'tiktok' | 'youtube';
  name: string;
  icon: React.ComponentType<{ className?: string }>;
  color: string;
  handleLabel: string;
  followersLabel: string;
  placeholder: string;
}

const SOCIAL_PLATFORMS: SocialPlatform[] = [
  {
    id: 'instagram',
    name: 'Instagram',
    icon: Instagram,
    color: 'bg-gradient-to-r from-purple-500 to-pink-500',
    handleLabel: 'Instagram handle',
    followersLabel: 'Broj pratilaca',
    placeholder: 'marko_markovic',
  },
  {
    id: 'tiktok',
    name: 'TikTok',
    icon: TikTokIcon,
    color: 'bg-black',
    handleLabel: 'TikTok handle',
    followersLabel: 'Broj pratilaca',
    placeholder: '@marko_markovic',
  },
  {
    id: 'youtube',
    name: 'YouTube',
    icon: Youtube,
    color: 'bg-red-600',
    handleLabel: 'YouTube kanal',
    followersLabel: 'Broj pretplatnika',
    placeholder: 'Marko Markovic',
  },
];

const socialMediaSchema = z.object({
  handle: z.string().min(1, 'Handle je obavezan'),
  followers: z.number().min(0, 'Broj pratilaca mora biti pozitivan broj'),
});

type SocialMediaForm = z.infer<typeof socialMediaSchema>;

interface SocialMediaData {
  platform: 'instagram' | 'tiktok' | 'youtube';
  handle: string;
  followers: number;
}

interface SocialMediaStepProps {
  onNext: (data: { socialMedia: SocialMediaData[] }) => void;
  initialData?: { socialMedia: SocialMediaData[] };
}

export function SocialMediaStep({ onNext, initialData }: SocialMediaStepProps) {
  const [addedPlatforms, setAddedPlatforms] = useState<SocialMediaData[]>(
    initialData?.socialMedia || []
  );
  const [activePlatform, setActivePlatform] = useState<'instagram' | 'tiktok' | 'youtube' | null>(null);

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    setValue,
  } = useForm<SocialMediaForm>({
    resolver: zodResolver(socialMediaSchema),
  });

  const handleAddPlatform = (platformId: 'instagram' | 'tiktok' | 'youtube') => {
    // Check if platform is already added
    if (addedPlatforms.some(p => p.platform === platformId)) {
      return;
    }
    
    setActivePlatform(platformId);
    reset();
  };

  const handleSavePlatform = (data: SocialMediaForm) => {
    if (!activePlatform) return;

    const newPlatform: SocialMediaData = {
      platform: activePlatform,
      handle: data.handle,
      followers: data.followers,
    };

    setAddedPlatforms(prev => [...prev, newPlatform]);
    setActivePlatform(null);
    reset();
  };

  const handleRemovePlatform = (platformId: 'instagram' | 'tiktok' | 'youtube') => {
    setAddedPlatforms(prev => prev.filter(p => p.platform !== platformId));
  };

  const handleCancelAdd = () => {
    setActivePlatform(null);
    reset();
  };

  const handleNext = () => {
    onNext({ socialMedia: addedPlatforms });
  };

  const getAvailablePlatforms = () => {
    return SOCIAL_PLATFORMS.filter(
      platform => !addedPlatforms.some(added => added.platform === platform.id)
    );
  };

  const getPlatformData = (platformId: 'instagram' | 'tiktok' | 'youtube') => {
    return SOCIAL_PLATFORMS.find(p => p.id === platformId);
  };

  return (
    <div className="space-y-6">
      {/* Added platforms */}
      {addedPlatforms.length > 0 && (
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Dodane platforme</h3>
          <div className="space-y-3">
            {addedPlatforms.map((platform) => {
              const platformData = getPlatformData(platform.platform);
              if (!platformData) return null;

              const Icon = platformData.icon;

              return (
                <Card key={platform.platform}>
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className={cn(
                          'w-10 h-10 rounded-lg flex items-center justify-center text-white',
                          platformData.color
                        )}>
                          <Icon className="h-5 w-5" />
                        </div>
                        <div>
                          <p className="font-medium">{platformData.name}</p>
                          <p className="text-sm text-gray-600">
                            @{platform.handle} • {platform.followers.toLocaleString()} pratilaca
                          </p>
                        </div>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleRemovePlatform(platform.platform)}
                        className="text-red-600 hover:text-red-700 hover:bg-red-50"
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </div>
      )}

      {/* Active platform form */}
      {activePlatform && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              {(() => {
                const platformData = getPlatformData(activePlatform);
                if (!platformData) return null;
                const Icon = platformData.icon;
                return (
                  <>
                    <div className={cn(
                      'w-8 h-8 rounded-lg flex items-center justify-center text-white',
                      platformData.color
                    )}>
                      <Icon className="h-4 w-4" />
                    </div>
                    <span>Dodaj {platformData.name}</span>
                  </>
                );
              })()}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit(handleSavePlatform)} className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="handle">
                    {getPlatformData(activePlatform)?.handleLabel}
                  </Label>
                  <Input
                    id="handle"
                    placeholder={getPlatformData(activePlatform)?.placeholder}
                    {...register('handle')}
                    className={errors.handle ? 'border-red-500' : ''}
                  />
                  {errors.handle && (
                    <p className="text-sm text-red-500">{errors.handle.message}</p>
                  )}
                </div>
                <div className="space-y-2">
                  <Label htmlFor="followers">
                    {getPlatformData(activePlatform)?.followersLabel}
                  </Label>
                  <Input
                    id="followers"
                    type="number"
                    placeholder="1000"
                    {...register('followers', { valueAsNumber: true })}
                    className={errors.followers ? 'border-red-500' : ''}
                  />
                  {errors.followers && (
                    <p className="text-sm text-red-500">{errors.followers.message}</p>
                  )}
                </div>
              </div>
              <div className="flex space-x-2">
                <Button type="submit">Dodaj</Button>
                <Button type="button" variant="outline" onClick={handleCancelAdd}>
                  Otkaži
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      )}

      {/* Available platforms */}
      {!activePlatform && getAvailablePlatforms().length > 0 && (
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Dodaj društvene mreže</h3>
          <p className="text-sm text-gray-600">
            Kliknite na platformu koju želite dodati:
          </p>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {getAvailablePlatforms().map((platform) => {
              const Icon = platform.icon;
              return (
                <Button
                  key={platform.id}
                  variant="outline"
                  className="h-auto p-4 flex flex-col items-center space-y-2"
                  onClick={() => handleAddPlatform(platform.id)}
                >
                  <div className={cn(
                    'w-12 h-12 rounded-lg flex items-center justify-center text-white',
                    platform.color
                  )}>
                    <Icon className="h-6 w-6" />
                  </div>
                  <span className="font-medium">{platform.name}</span>
                  <div className="flex items-center space-x-1 text-xs text-gray-500">
                    <Plus className="h-3 w-3" />
                    <span>Dodaj</span>
                  </div>
                </Button>
              );
            })}
          </div>
        </div>
      )}

      {/* Continue without adding platforms */}
      {!activePlatform && (
        <div className="space-y-4">
          {addedPlatforms.length === 0 && (
            <div className="text-center py-8">
              <p className="text-gray-600 mb-4">
                Možete preskočiti ovaj korak i dodati društvene mreže kasnije.
              </p>
            </div>
          )}
          
          <div className="flex justify-center">
            <Button onClick={handleNext} className="min-w-32">
              {addedPlatforms.length > 0 ? 'Nastavi' : 'Preskoči'}
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}
