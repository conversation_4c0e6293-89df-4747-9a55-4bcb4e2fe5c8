'use client';

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { CategoryGridSelector } from '@/components/ui/category-grid-selector';
import { CountrySelector } from '@/components/ui/country-selector';
import { validateUsername, generateUsernameSuggestions } from '@/lib/username-validation';
import { Check, X, Loader2, RefreshCw } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useAuth } from '@/contexts/AuthContext';

const basicInfoSchema = z.object({
  fullName: z.string().min(2, 'Ime i prezime mora imati najmanje 2 karaktera'),
  username: z
    .string()
    .min(3, 'Username mora imati najmanje 3 karaktera')
    .max(20, 'Username može imati maksimalno 20 karaktera')
    .regex(
      /^[a-zA-Z0-9_]+$/,
      'Username može sadržavati samo slova, brojeve i _'
    ),
  bio: z
    .string()
    .max(500, 'Bio može imati maksimalno 500 karaktera')
    .optional(),
  country: z
    .string()
    .min(1, 'Morate izabrati državu'),
  city: z
    .string()
    .max(100, 'Grad može imati maksimalno 100 karaktera')
    .optional(),
});

type BasicInfoForm = z.infer<typeof basicInfoSchema>;

interface BasicInfoStepProps {
  onNext: (data: BasicInfoForm & { categories: number[] }) => void;
  initialData?: Partial<BasicInfoForm & { categories: number[] }>;
}

export function BasicInfoStep({ onNext, initialData }: BasicInfoStepProps) {
  const { user } = useAuth();
  const [selectedCategories, setSelectedCategories] = useState<number[]>(
    initialData?.categories || []
  );
  const [usernameValidation, setUsernameValidation] = useState<{
    isValid: boolean;
    isAvailable: boolean;
    message: string;
    type: 'success' | 'error' | 'warning';
  } | null>(null);
  const [isValidatingUsername, setIsValidatingUsername] = useState(false);
  const [usernameSuggestions, setUsernameSuggestions] = useState<string[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
    setValue,
    setError,
    clearErrors,
  } = useForm<BasicInfoForm>({
    resolver: zodResolver(basicInfoSchema),
    defaultValues: {
      username: initialData?.username || '',
      bio: initialData?.bio || '',
      country: initialData?.country || '',
      city: initialData?.city || '',
    },
  });

  const watchedUsername = watch('username');

  // Generate username suggestions on mount
  useEffect(() => {
    if (user?.user_metadata?.full_name && !initialData?.username) {
      const suggestions = generateUsernameSuggestions(user.user_metadata.full_name);
      setUsernameSuggestions(suggestions);
    }
  }, [user, initialData]);

  // Validate username on change
  useEffect(() => {
    if (!watchedUsername || watchedUsername.length < 3) {
      setUsernameValidation(null);
      setShowSuggestions(false);
      return;
    }

    const validateUsernameAsync = async () => {
      setIsValidatingUsername(true);
      try {
        const result = await validateUsername(watchedUsername, user?.id);
        setUsernameValidation(result);
        
        if (!result.isValid || !result.isAvailable) {
          setError('username', { message: result.message });
          setShowSuggestions(true);
        } else {
          clearErrors('username');
          setShowSuggestions(false);
        }
      } catch (error) {
        console.error('Username validation error:', error);
      } finally {
        setIsValidatingUsername(false);
      }
    };

    const timeoutId = setTimeout(validateUsernameAsync, 500);
    return () => clearTimeout(timeoutId);
  }, [watchedUsername, user?.id, setError, clearErrors]);

  const onSubmit = (data: BasicInfoForm) => {
    // Validate categories
    if (selectedCategories.length === 0) {
      setError('root', {
        message: 'Morate izabrati najmanje jednu kategoriju',
      });
      return;
    }

    // Validate username
    if (!usernameValidation?.isValid || !usernameValidation?.isAvailable) {
      setError('username', {
        message: 'Username nije valjan ili nije dostupan',
      });
      return;
    }

    onNext({
      ...data,
      categories: selectedCategories,
    });
  };

  const handleSuggestionClick = (suggestion: string) => {
    setValue('username', suggestion);
    setShowSuggestions(false);
  };

  const refreshSuggestions = () => {
    if (user?.user_metadata?.full_name) {
      const suggestions = generateUsernameSuggestions(user.user_metadata.full_name);
      setUsernameSuggestions(suggestions);
    }
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      {/* Username */}
      <div className="space-y-2">
        <Label htmlFor="username">Username *</Label>
        <div className="relative">
          <Input
            id="username"
            placeholder="marko_markovic"
            {...register('username')}
            className={cn(
              errors.username && 'border-red-500',
              usernameValidation?.isValid && usernameValidation?.isAvailable && 'border-green-500'
            )}
          />
          <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
            {isValidatingUsername ? (
              <Loader2 className="h-4 w-4 animate-spin text-gray-400" />
            ) : usernameValidation?.isValid && usernameValidation?.isAvailable ? (
              <Check className="h-4 w-4 text-green-500" />
            ) : usernameValidation && (!usernameValidation.isValid || !usernameValidation.isAvailable) ? (
              <X className="h-4 w-4 text-red-500" />
            ) : null}
          </div>
        </div>
        
        {usernameValidation && (
          <p className={cn(
            'text-sm',
            usernameValidation.type === 'success' && 'text-green-600',
            usernameValidation.type === 'error' && 'text-red-600',
            usernameValidation.type === 'warning' && 'text-yellow-600'
          )}>
            {usernameValidation.message}
          </p>
        )}

        {errors.username && (
          <p className="text-sm text-red-500">{errors.username.message}</p>
        )}

        {/* Username suggestions */}
        {showSuggestions && usernameSuggestions.length > 0 && (
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <p className="text-sm text-gray-600">Predlozi:</p>
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={refreshSuggestions}
                className="h-6 px-2"
              >
                <RefreshCw className="h-3 w-3" />
              </Button>
            </div>
            <div className="flex flex-wrap gap-2">
              {usernameSuggestions.map((suggestion) => (
                <Button
                  key={suggestion}
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => handleSuggestionClick(suggestion)}
                  className="h-7 px-3 text-xs"
                >
                  {suggestion}
                </Button>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Categories */}
      <div className="space-y-2">
        <Label>Kategorije *</Label>
        <CategoryGridSelector
          selectedCategories={selectedCategories}
          onCategoriesChange={setSelectedCategories}
          maxCategories={3}
        />
      </div>

      {/* Country */}
      <div className="space-y-2">
        <Label htmlFor="country">Država *</Label>
        <CountrySelector
          value={watch('country')}
          onValueChange={(value) => setValue('country', value)}
          placeholder="Izaberite državu..."
        />
        {errors.country && (
          <p className="text-sm text-red-500">{errors.country.message}</p>
        )}
      </div>

      {/* Bio */}
      <div className="space-y-2">
        <Label htmlFor="bio">Bio</Label>
        <Textarea
          id="bio"
          placeholder="Opišite sebe u nekoliko rečenica..."
          rows={3}
          {...register('bio')}
          className={errors.bio ? 'border-red-500' : ''}
        />
        <p className="text-xs text-gray-500">
          Ovo možete i kasnije urediti u postavkama profila.
        </p>
        {errors.bio && (
          <p className="text-sm text-red-500">{errors.bio.message}</p>
        )}
      </div>

      {/* City */}
      <div className="space-y-2">
        <Label htmlFor="city">Grad (opcionalno)</Label>
        <Input
          id="city"
          placeholder="Sarajevo"
          {...register('city')}
          className={errors.city ? 'border-red-500' : ''}
        />
        <p className="text-xs text-gray-500">
          Ovo možete i kasnije urediti u postavkama profila.
        </p>
        {errors.city && (
          <p className="text-sm text-red-500">{errors.city.message}</p>
        )}
      </div>

      {/* Root errors */}
      {errors.root && (
        <p className="text-sm text-red-500">{errors.root.message}</p>
      )}

      {/* Hidden submit button - form will be submitted by parent component */}
      <button type="submit" className="hidden" />
    </form>
  );
}
