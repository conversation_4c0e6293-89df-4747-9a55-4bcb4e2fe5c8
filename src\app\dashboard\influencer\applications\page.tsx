'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import {
  Eye,
  Clock,
  CheckCircle,
  XCircle,
  Calendar,
  DollarSign,
} from 'lucide-react';
import Link from 'next/link';
import { DashboardLayout } from '@/components/dashboard/DashboardLayout';
import { supabase } from '@/lib/supabase';

interface Application {
  id: string;
  campaign_id: string;
  status: 'pending' | 'accepted' | 'rejected';
  proposed_rate: string;
  proposal_text: string;
  delivery_timeframe: string;
  portfolio_links: string[] | null;
  experience_relevant: string | null;
  audience_insights: string | null;
  applied_at: string;
  campaigns: {
    id: string;
    title: string;
    budget: number;
    business_id: string;
  };
}

export default function InfluencerApplicationsPage() {
  const { user } = useAuth();
  const [applications, setApplications] = useState<Application[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (user?.id) {
      fetchApplications();
    }
  }, [user?.id]);

  const fetchApplications = async () => {
    try {
      const { data, error } = await supabase
        .from('campaign_applications')
        .select(`
          *,
          campaigns (
            id,
            title,
            budget,
            business_id
          )
        `)
        .eq('influencer_id', user?.id)
        .order('applied_at', { ascending: false });

      if (error) throw error;
      setApplications(data || []);
    } catch (error) {
      console.error('Error fetching applications:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="h-4 w-4" />;
      case 'accepted':
        return <CheckCircle className="h-4 w-4" />;
      case 'rejected':
        return <XCircle className="h-4 w-4" />;
      default:
        return <Clock className="h-4 w-4" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'accepted':
        return 'bg-green-100 text-green-800';
      case 'rejected':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending':
        return 'Na čekanju';
      case 'accepted':
        return 'Prihvaćeno';
      case 'rejected':
        return 'Odbačeno';
      default:
        return status;
    }
  };

  const pendingCount = applications.filter(app => app.status === 'pending').length;
  const acceptedCount = applications.filter(app => app.status === 'accepted').length;
  const rejectedCount = applications.filter(app => app.status === 'rejected').length;

  if (loading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
            <p className="mt-2 text-muted-foreground">Učitavanje...</p>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Moje aplikacije</h1>
          <p className="text-muted-foreground">
            Pregledajte status vaših aplikacija na kampanje
          </p>
        </div>

        {/* Stats Cards */}
        <div className="grid gap-4 md:grid-cols-4">
          <Card>
            <CardContent className="flex items-center p-6">
              <div className="flex items-center space-x-2">
                <p className="text-sm font-medium">Ukupno</p>
                <p className="text-2xl font-bold">{applications.length}</p>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="flex items-center p-6">
              <div className="flex items-center space-x-2">
                <p className="text-sm font-medium">Na čekanju</p>
                <p className="text-2xl font-bold">{pendingCount}</p>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="flex items-center p-6">
              <div className="flex items-center space-x-2">
                <p className="text-sm font-medium">Prihvaćeno</p>
                <p className="text-2xl font-bold">{acceptedCount}</p>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="flex items-center p-6">
              <div className="flex items-center space-x-2">
                <p className="text-sm font-medium">Odbačeno</p>
                <p className="text-2xl font-bold">{rejectedCount}</p>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Applications List */}
        <Card>
          <CardHeader>
            <CardTitle>Aplikacije</CardTitle>
            <CardDescription>Pregled svih vaših aplikacija na kampanje</CardDescription>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="all" className="w-full">
              <TabsList className="grid w-full grid-cols-4">
                <TabsTrigger value="all">Sve ({applications.length})</TabsTrigger>
                <TabsTrigger value="pending">Na čekanju ({pendingCount})</TabsTrigger>
                <TabsTrigger value="accepted">Prihvaćeno ({acceptedCount})</TabsTrigger>
                <TabsTrigger value="rejected">Odbačeno ({rejectedCount})</TabsTrigger>
              </TabsList>
              
              <TabsContent value="all" className="space-y-4">
                {applications.length === 0 ? (
                  <div className="text-center py-8">
                    <p className="text-muted-foreground">Nemate aplikacija još uvijek.</p>
                  </div>
                ) : (
                  applications.map((application) => (
                    <ApplicationCard key={application.id} application={application} />
                  ))
                )}
              </TabsContent>
              
              <TabsContent value="pending" className="space-y-4">
                {applications.filter(app => app.status === 'pending').map((application) => (
                  <ApplicationCard key={application.id} application={application} />
                ))}
              </TabsContent>
              
              <TabsContent value="accepted" className="space-y-4">
                {applications.filter(app => app.status === 'accepted').map((application) => (
                  <ApplicationCard key={application.id} application={application} />
                ))}
              </TabsContent>
              
              <TabsContent value="rejected" className="space-y-4">
                {applications.filter(app => app.status === 'rejected').map((application) => (
                  <ApplicationCard key={application.id} application={application} />
                ))}
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
}

function ApplicationCard({ application }: { application: Application }) {
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="h-4 w-4" />;
      case 'accepted':
        return <CheckCircle className="h-4 w-4" />;
      case 'rejected':
        return <XCircle className="h-4 w-4" />;
      default:
        return <Clock className="h-4 w-4" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'accepted':
        return 'bg-green-100 text-green-800';
      case 'rejected':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending':
        return 'Na čekanju';
      case 'accepted':
        return 'Prihvaćeno';
      case 'rejected':
        return 'Odbačeno';
      default:
        return status;
    }
  };

  return (
    <div className="flex items-center justify-between p-4 border rounded-lg">
      <div className="flex-1">
        <div className="flex items-center space-x-3 mb-2">
          <h3 className="font-semibold">{application.campaigns.title}</h3>
          <Badge className={`flex items-center space-x-1 ${getStatusColor(application.status)}`}>
            {getStatusIcon(application.status)}
            <span>{getStatusText(application.status)}</span>
          </Badge>
        </div>
        
        <div className="flex items-center space-x-4 text-sm text-muted-foreground">
          <div className="flex items-center space-x-1">
            <DollarSign className="h-4 w-4" />
            <span>{application.proposed_rate} KM</span>
          </div>
          <div className="flex items-center space-x-1">
            <Clock className="h-4 w-4" />
            <span>{application.delivery_timeframe}</span>
          </div>
          <div className="flex items-center space-x-1">
            <Calendar className="h-4 w-4" />
            <span>{new Date(application.applied_at).toLocaleDateString('bs-BA')}</span>
          </div>
        </div>
      </div>
      
      <Link href={`/dashboard/influencer/applications/${application.id}`}>
        <Button variant="outline" size="sm" className="flex items-center space-x-2">
          <Eye className="h-4 w-4" />
          <span>Pregled</span>
        </Button>
      </Link>
    </div>
  );
}
