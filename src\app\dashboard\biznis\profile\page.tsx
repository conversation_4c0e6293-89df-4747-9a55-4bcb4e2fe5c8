'use client';

import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { DashboardLayout } from '@/components/dashboard/DashboardLayout';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useAuth } from '@/contexts/AuthContext';
import {
  getProfile,
  updateProfile,
  getBusiness,
  updateBusiness,
  getBusinessTargetCategories,
  getBusinessPlatforms,
  updateBusinessTargetCategories,
  updateBusinessPlatforms,
} from '@/lib/profiles';
import {
  Loader2,
  Save,
  Building,
  Globe,
  Users,
  DollarSign,
} from 'lucide-react';
import { toast } from 'sonner';

const profileSchema = z.object({
  username: z.string().min(3, 'Username mora imati najmanje 3 karaktera'),
  bio: z
    .string()
    .max(500, 'Bio može imati maksimalno 500 karaktera')
    .optional(),
  city: z.string().optional(),
  country: z.string().optional(),
  website_url: z
    .string()
    .url('Neispravna URL adresa')
    .optional()
    .or(z.literal('')),
  company_name: z
    .string()
    .min(2, 'Naziv kompanije mora imati najmanje 2 karaktera'),
  // Social media handles
  instagram_handle: z.string().optional(),
  instagram_followers: z.number().min(0).optional(),
  tiktok_handle: z.string().optional(),
  tiktok_followers: z.number().min(0).optional(),
  youtube_handle: z.string().optional(),
  youtube_subscribers: z.number().min(0).optional(),
});

type ProfileForm = z.infer<typeof profileSchema>;



export default function BusinessProfilePage() {
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [profile, setProfile] = useState<any>(null);
  const [business, setBusiness] = useState<any>(null);
  const [categories, setCategories] = useState<any[]>([]);
  const [platforms, setPlatforms] = useState<any[]>([]);

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    setValue,
    watch,
  } = useForm<ProfileForm>({
    resolver: zodResolver(profileSchema),
  });

  useEffect(() => {
    if (user) {
      loadData();
    }
  }, [user]);

  const loadData = async () => {
    try {
      setLoading(true);

      // Load profile
      const { data: profileData, error: profileError } = await getProfile(
        user!.id
      );
      if (profileError || !profileData) {
        toast.error('Greška pri učitavanju profila');
        return;
      }
      setProfile(profileData);

      // Load business data
      const { data: businessData, error: businessError } = await getBusiness(
        user!.id
      );
      if (businessError || !businessData) {
        toast.error('Greška pri učitavanju business podataka');
        return;
      }
      setBusiness(businessData);

      // Load business categories
      const { data: categoriesData, error: categoriesError } = await getBusinessTargetCategories(
        user!.id
      );
      if (categoriesError) {
        console.error('Error loading categories:', categoriesError);
      } else {
        setCategories(categoriesData || []);
      }

      // Load business platforms
      const { data: platformsData, error: platformsError } = await getBusinessPlatforms(
        user!.id
      );
      if (platformsError) {
        console.error('Error loading platforms:', platformsError);
      } else {
        setPlatforms(platformsData || []);
      }

      // Find platform data for form
      const instagramPlatform = platformsData?.find(p => p.platform_id === 1);
      const tiktokPlatform = platformsData?.find(p => p.platform_id === 2);
      const youtubePlatform = platformsData?.find(p => p.platform_id === 3);

      // Popuni formu sa postojećim podacima
      reset({
        username: profileData.username || '',
        bio: profileData.bio || '',
        city: profileData.city || '',
        country: profileData.country || '',
        website_url: profileData.website_url || '',
        company_name: businessData.company_name || '',
        instagram_handle: instagramPlatform?.handle || '',
        instagram_followers: instagramPlatform?.followers_count || 0,
        tiktok_handle: tiktokPlatform?.handle || '',
        tiktok_followers: tiktokPlatform?.followers_count || 0,
        youtube_handle: youtubePlatform?.handle || '',
        youtube_subscribers: youtubePlatform?.followers_count || 0,
      });
    } catch (err) {
      toast.error('Neočekivana greška');
    } finally {
      setLoading(false);
    }
  };

  const onSubmit = async (data: ProfileForm) => {
    if (!user) return;

    setSaving(true);
    try {
      // Update profile
      const { error: profileError } = await updateProfile(user.id, {
        username: data.username,
        bio: data.bio || null,
        city: data.city || null,
        country: data.country || null,
        website_url: data.website_url || null,
      });

      if (profileError) {
        if (profileError.message.includes('username')) {
          toast.error('Username je već zauzet');
        } else {
          toast.error('Greška pri ažuriranju profila');
        }
        return;
      }

      // Update business data
      const { error: businessError } = await updateBusiness(user.id, {
        company_name: data.company_name,
      });

      if (businessError) {
        toast.error('Greška pri ažuriranju business podataka');
        return;
      }

      // Update business platforms
      const platformUpdates = [];
      if (data.instagram_handle) {
        platformUpdates.push({
          platform_id: 1,
          handle: data.instagram_handle,
          followers_count: data.instagram_followers || 0,
        });
      }
      if (data.tiktok_handle) {
        platformUpdates.push({
          platform_id: 2,
          handle: data.tiktok_handle,
          followers_count: data.tiktok_followers || 0,
        });
      }
      if (data.youtube_handle) {
        platformUpdates.push({
          platform_id: 3,
          handle: data.youtube_handle,
          followers_count: data.youtube_subscribers || 0,
        });
      }

      const { error: platformsError } = await updateBusinessPlatforms(user.id, platformUpdates);
      if (platformsError) {
        console.error('Error updating platforms:', platformsError);
        toast.error('Greška pri ažuriranju social media podataka');
        return;
      }

      toast.success('Profil je uspješno ažuriran');
      loadData(); // Refresh data
    } catch (err) {
      toast.error('Neočekivana greška');
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <DashboardLayout requiredUserType="business">
        <div className="flex items-center justify-center h-64">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout requiredUserType="business">
      <div className="max-w-4xl mx-auto space-y-6">
        {/* Header */}
        <div>
          <h1 className="text-3xl font-bold">Postavke profila</h1>
          <p className="text-muted-foreground mt-2">
            Upravljajte svojim javnim profilom koji vide influenceri
          </p>
        </div>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          {/* Osnovne informacije */}
          <Card>
            <CardHeader>
              <div className="flex items-center space-x-2">
                <Building className="h-5 w-5" />
                <CardTitle>Osnovne informacije</CardTitle>
              </div>
              <CardDescription>
                Ovi podaci se prikazuju na vašem javnom profilu
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="username">Username *</Label>
                  <Input
                    id="username"
                    {...register('username')}
                    placeholder="@vasabrand"
                  />
                  {errors.username && (
                    <p className="text-sm text-destructive mt-1">
                      {errors.username.message}
                    </p>
                  )}
                </div>

                <div>
                  <Label htmlFor="company_name">Naziv kompanije *</Label>
                  <Input
                    id="company_name"
                    {...register('company_name')}
                    placeholder="Vaša kompanija d.o.o."
                  />
                  {errors.company_name && (
                    <p className="text-sm text-destructive mt-1">
                      {errors.company_name.message}
                    </p>
                  )}
                </div>
              </div>

              <div>
                <Label htmlFor="bio">Opis kompanije</Label>
                <Textarea
                  id="bio"
                  {...register('bio')}
                  placeholder="Opišite vašu kompaniju i čime se bavite..."
                  rows={4}
                />
                {errors.bio && (
                  <p className="text-sm text-destructive mt-1">
                    {errors.bio.message}
                  </p>
                )}
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <Label htmlFor="city">Grad</Label>
                  <Input
                    id="city"
                    {...register('city')}
                    placeholder="Sarajevo"
                  />
                </div>

                <div>
                  <Label htmlFor="country">Država</Label>
                  <Input
                    id="country"
                    {...register('country')}
                    placeholder="Bosna i Hercegovina"
                  />
                </div>

                <div>
                  <Label htmlFor="website_url">Website</Label>
                  <Input
                    id="website_url"
                    {...register('website_url')}
                    placeholder="https://vaswebsite.com"
                  />
                  {errors.website_url && (
                    <p className="text-sm text-destructive mt-1">
                      {errors.website_url.message}
                    </p>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Kategorije */}
          <Card>
            <CardHeader>
              <div className="flex items-center space-x-2">
                <Users className="h-5 w-5" />
                <CardTitle>Kategorije/Industrija</CardTitle>
              </div>
              <CardDescription>
                Kategorije koje ste izabrali tokom onboardinga
              </CardDescription>
            </CardHeader>
            <CardContent>
              {categories.length > 0 ? (
                <div className="flex flex-wrap gap-2">
                  {categories.map((category) => (
                    <div
                      key={category.category_id}
                      className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-blue-100 text-blue-800"
                    >
                      {category.categories?.icon && (
                        <span className="mr-1">{category.categories.icon}</span>
                      )}
                      {category.categories?.name}
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-muted-foreground">Nema izabranih kategorija</p>
              )}
            </CardContent>
          </Card>

          {/* Social Media */}
          <Card>
            <CardHeader>
              <div className="flex items-center space-x-2">
                <Globe className="h-5 w-5" />
                <CardTitle>Društvene mreže</CardTitle>
              </div>
              <CardDescription>
                Vaši profili na društvenim mrežama
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Instagram */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="instagram_handle">Instagram handle</Label>
                  <Input
                    id="instagram_handle"
                    {...register('instagram_handle')}
                    placeholder="vasabrand"
                  />
                </div>
                <div>
                  <Label htmlFor="instagram_followers">Instagram pratilaca</Label>
                  <Input
                    id="instagram_followers"
                    type="number"
                    {...register('instagram_followers', { valueAsNumber: true })}
                    placeholder="0"
                    min="0"
                  />
                </div>
              </div>

              {/* TikTok */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="tiktok_handle">TikTok handle</Label>
                  <Input
                    id="tiktok_handle"
                    {...register('tiktok_handle')}
                    placeholder="vasabrand"
                  />
                </div>
                <div>
                  <Label htmlFor="tiktok_followers">TikTok pratilaca</Label>
                  <Input
                    id="tiktok_followers"
                    type="number"
                    {...register('tiktok_followers', { valueAsNumber: true })}
                    placeholder="0"
                    min="0"
                  />
                </div>
              </div>

              {/* YouTube */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="youtube_handle">YouTube handle</Label>
                  <Input
                    id="youtube_handle"
                    {...register('youtube_handle')}
                    placeholder="vasabrand"
                  />
                </div>
                <div>
                  <Label htmlFor="youtube_subscribers">YouTube pretplatnika</Label>
                  <Input
                    id="youtube_subscribers"
                    type="number"
                    {...register('youtube_subscribers', { valueAsNumber: true })}
                    placeholder="0"
                    min="0"
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Save Button */}
          <div className="flex justify-end">
            <Button type="submit" disabled={saving}>
              {saving ? (
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <Save className="h-4 w-4 mr-2" />
              )}
              Sačuvaj promjene
            </Button>
          </div>
        </form>
      </div>
    </DashboardLayout>
  );
}
