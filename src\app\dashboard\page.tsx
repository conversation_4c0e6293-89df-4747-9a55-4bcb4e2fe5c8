'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { getOrCreateProfile } from '@/lib/profiles';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Loader2, User, Building2, Settings, LogOut } from 'lucide-react';

export default function DashboardPage() {
  const router = useRouter();
  const { user, signOut, loading: authLoading } = useAuth();
  const [profile, setProfile] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!authLoading && !user) {
      router.push('/prijava');
      return;
    }

    if (user) {
      loadProfile();
    }
  }, [user, authLoading, router]);

  const loadProfile = async () => {
    try {
      setLoading(true);
      const { data, error } = await getOrCreateProfile(user!.id);

      console.log('Profile data:', data);
      console.log('Profile error:', error);

      if (error) {
        console.error('Profile loading error:', error);
        setError('Greška pri učitavanju profila: ' + error.message);
        return;
      }

      if (!data) {
        console.log('No profile data, redirecting to profile creation');
        router.push('/profil/kreiranje');
        return;
      }

      // Check if profile is completed
      if (!data.profile_completed) {
        console.log('Profile not completed, redirecting to onboarding');
        if (data.user_type === 'influencer') {
          router.push('/profil/kreiranje/influencer/onboarding');
        } else if (data.user_type === 'business') {
          router.push('/profil/kreiranje/biznis/onboarding');
        } else {
          router.push('/profil/kreiranje');
        }
        return;
      }

      setProfile(data);

      // Redirect to appropriate dashboard based on user type
      if (data.user_type === 'influencer') {
        router.push('/dashboard/influencer');
      } else if (data.user_type === 'business') {
        router.push('/dashboard/biznis');
      }
    } catch (err) {
      console.error('Unexpected error in loadProfile:', err);
      setError('Neočekivana greška');
    } finally {
      setLoading(false);
    }
  };

  const handleSignOut = async () => {
    try {
      await signOut();
      router.push('/');
    } catch (error) {
      console.error('Error signing out:', error);
    }
  };

  if (authLoading || loading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p className="text-muted-foreground">Učitavanje...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <CardTitle className="text-red-500">Greška</CardTitle>
            <CardDescription>{error}</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <Button onClick={loadProfile} className="w-full">
              Pokušaj ponovo
            </Button>
            <Button
              onClick={handleSignOut}
              variant="outline"
              className="w-full"
            >
              <LogOut className="mr-2 h-4 w-4" />
              Odjavi se
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  // This should not be reached as we redirect above, but just in case
  return (
    <div className="min-h-screen bg-background flex items-center justify-center">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
            <User className="h-8 w-8 text-primary" />
          </div>
          <CardTitle>Dobrodošli!</CardTitle>
          <CardDescription>
            Preusmjeravamo vas na odgovarajući dashboard...
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex justify-center">
            <Loader2 className="h-6 w-6 animate-spin" />
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
