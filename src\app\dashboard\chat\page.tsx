'use client';

import { useState } from 'react';
import { useSearchParams } from 'next/navigation';
import { DashboardLayout } from '@/components/dashboard/DashboardLayout';
import { Chat } from '@/components/chat/Chat';

export default function ChatPage() {
  const searchParams = useSearchParams();
  const roomId = searchParams.get('room');
  const [isInChatRoom, setIsInChatRoom] = useState(!!roomId);

  return (
    <DashboardLayout hideHeader={isInChatRoom}>
      <Chat
        initialRoomId={roomId || undefined}
        onRoomStateChange={setIsInChatRoom}
      />
    </DashboardLayout>
  );
}
