'use client';

import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

interface AgeStepProps {
  value: number | null;
  onChange: (value: number) => void;
  onNext: () => void;
  onBack: () => void;
}

export function AgeStep({ value, onChange, onNext, onBack }: AgeStepProps) {
  // Generate age options from 13 to 100
  const ageOptions = Array.from({ length: 88 }, (_, i) => i + 13);

  const handleNext = () => {
    if (value && value >= 13) {
      onNext();
    }
  };

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">
          Koliko imate godina?
        </h2>
        <p className="text-gray-600">
          Ova informacija pomaže brendovima da pronađu odgovarajuće influencere
        </p>
      </div>

      <div className="space-y-4">
        <div>
          <Label htmlFor="age">Godine</Label>
          <Select
            value={value ? value.toString() : ''}
            onValueChange={(val) => onChange(parseInt(val))}
          >
            <SelectTrigger>
              <SelectValue placeholder="Izaberite godine" />
            </SelectTrigger>
            <SelectContent className="max-h-60">
              {ageOptions.map((age) => (
                <SelectItem key={age} value={age.toString()}>
                  {age} {age === 1 ? 'godina' : age < 5 ? 'godine' : 'godina'}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div className="text-sm text-gray-500">
          <p>Minimalne godine za korišćenje platforme su 13.</p>
        </div>
      </div>

      <div className="flex gap-3">
        <Button
          variant="outline"
          onClick={onBack}
          className="flex-1"
        >
          Nazad
        </Button>
        <Button
          onClick={handleNext}
          disabled={!value || value < 13}
          className="flex-1"
        >
          Dalje
        </Button>
      </div>
    </div>
  );
}
