-- Add gender and age fields to profiles table
-- This migration adds gender and age fields to the profiles table with proper constraints

-- 1. Add gender column to profiles table
ALTER TABLE profiles 
ADD COLUMN IF NOT EXISTS gender VARCHAR(20);

-- 2. Add age column to profiles table  
ALTER TABLE profiles 
ADD COLUMN IF NOT EXISTS age INTEGER;

-- 3. Add check constraint for gender values in profiles table
ALTER TABLE profiles 
ADD CONSTRAINT IF NOT EXISTS profiles_gender_check CHECK (
  gender IS NULL OR gender IN ('male', 'female', 'other', 'prefer_not_to_say')
);

-- 4. Add check constraint for age range in profiles table
ALTER TABLE profiles 
ADD CONSTRAINT IF NOT EXISTS profiles_age_check CHECK (
  age IS NULL OR (age >= 13 AND age <= 100)
);

-- 5. Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_profiles_gender ON profiles(gender);
CREATE INDEX IF NOT EXISTS idx_profiles_age ON profiles(age);

-- 6. Add comments for documentation
COMMENT ON COLUMN profiles.gender IS 'User gender (male, female, other, prefer_not_to_say)';
COMMENT ON COLUMN profiles.age IS 'User age (13-100 years)';
