'use client';

import { useEffect, useState } from 'react';
import { DashboardLayout } from '@/components/dashboard/DashboardLayout';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Send,
  Clock,
  CheckCircle,
  XCircle,
  Search,
  Filter,
  Eye,
  Calendar,
  DollarSign,
  MessageCircle,
} from 'lucide-react';
import { ChatEnableButton } from '@/components/chat/ChatEnableButton';
import { getBusinessOffers, type DirectOfferWithDetails } from '@/lib/offers';
import { formatDistanceToNow } from 'date-fns';
import { hr } from 'date-fns/locale';
import Link from 'next/link';

export default function BusinessOffersPage() {
  const [offers, setOffers] = useState<DirectOfferWithDetails[]>([]);
  const [filteredOffers, setFilteredOffers] = useState<
    DirectOfferWithDetails[]
  >([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [sortBy, setSortBy] = useState<string>('newest');

  useEffect(() => {
    loadOffers();
  }, []);

  useEffect(() => {
    filterAndSortOffers();
  }, [offers, searchTerm, statusFilter, sortBy]);

  const loadOffers = async () => {
    setIsLoading(true);
    try {
      const { data, error } = await getBusinessOffers();
      if (error) {
        console.error('Error loading offers:', error);
      } else {
        setOffers(data || []);
      }
    } catch (error) {
      console.error('Error loading offers:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const filterAndSortOffers = () => {
    let filtered = [...offers];

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(
        offer =>
          offer.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
          (offer.influencer.full_name &&
            offer.influencer.full_name
              .toLowerCase()
              .includes(searchTerm.toLowerCase())) ||
          offer.influencer.username
            .toLowerCase()
            .includes(searchTerm.toLowerCase())
      );
    }

    // Filter by status
    if (statusFilter !== 'all') {
      filtered = filtered.filter(offer => offer.status === statusFilter);
    }

    // Sort
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'newest':
          return (
            new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
          );
        case 'oldest':
          return (
            new Date(a.created_at).getTime() - new Date(b.created_at).getTime()
          );
        case 'budget_high':
          return b.budget - a.budget;
        case 'budget_low':
          return a.budget - b.budget;
        default:
          return 0;
      }
    });

    setFilteredOffers(filtered);
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return (
          <Badge
            variant="outline"
            className="text-yellow-600 border-yellow-600"
          >
            <Clock className="w-3 h-3 mr-1" />
            Na čekanju
          </Badge>
        );
      case 'accepted':
        return (
          <Badge variant="outline" className="text-green-600 border-green-600">
            <CheckCircle className="w-3 h-3 mr-1" />
            Prihvaćeno
          </Badge>
        );
      case 'rejected':
        return (
          <Badge variant="outline" className="text-red-600 border-red-600">
            <XCircle className="w-3 h-3 mr-1" />
            Odbijeno
          </Badge>
        );
      case 'completed':
        return (
          <Badge variant="outline" className="text-blue-600 border-blue-600">
            <CheckCircle className="w-3 h-3 mr-1" />
            Završeno
          </Badge>
        );
      case 'cancelled':
        return (
          <Badge variant="outline" className="text-gray-600 border-gray-600">
            <XCircle className="w-3 h-3 mr-1" />
            Otkazano
          </Badge>
        );
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const getInitials = (name: string | null) => {
    if (!name) return 'U';
    return name
      .split(' ')
      .map(word => word[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const stats = {
    total: offers.length,
    pending: offers.filter(o => o.status === 'pending').length,
    accepted: offers.filter(o => o.status === 'accepted').length,
    rejected: offers.filter(o => o.status === 'rejected').length,
  };

  if (isLoading) {
    return (
      <DashboardLayout requiredUserType="business">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout requiredUserType="business">
      <div className="space-y-6">
        {/* Header */}
        <div>
          <h1 className="text-2xl md:text-3xl font-bold">Moje ponude</h1>
          <p className="text-muted-foreground mt-1">
            Upravljajte vašim direktnim ponudama influencerima
          </p>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">Ukupno</p>
                  <p className="text-2xl font-bold">{stats.total}</p>
                </div>
                <Send className="h-8 w-8 text-muted-foreground" />
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">Na čekanju</p>
                  <p className="text-2xl font-bold text-yellow-600">
                    {stats.pending}
                  </p>
                </div>
                <Clock className="h-8 w-8 text-yellow-600" />
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">Prihvaćeno</p>
                  <p className="text-2xl font-bold text-green-600">
                    {stats.accepted}
                  </p>
                </div>
                <CheckCircle className="h-8 w-8 text-green-600" />
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">Odbijeno</p>
                  <p className="text-2xl font-bold text-red-600">
                    {stats.rejected}
                  </p>
                </div>
                <XCircle className="h-8 w-8 text-red-600" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <Card>
          <CardContent className="p-4">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Pretraži po nazivu ponude ili influenceru..."
                    value={searchTerm}
                    onChange={e => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-full md:w-48">
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Svi statusi</SelectItem>
                  <SelectItem value="pending">Na čekanju</SelectItem>
                  <SelectItem value="accepted">Prihvaćeno</SelectItem>
                  <SelectItem value="rejected">Odbijeno</SelectItem>
                  <SelectItem value="completed">Završeno</SelectItem>
                  <SelectItem value="cancelled">Otkazano</SelectItem>
                </SelectContent>
              </Select>
              <Select value={sortBy} onValueChange={setSortBy}>
                <SelectTrigger className="w-full md:w-48">
                  <SelectValue placeholder="Sortiraj" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="newest">Najnovije</SelectItem>
                  <SelectItem value="oldest">Najstarije</SelectItem>
                  <SelectItem value="budget_high">
                    Budžet (visok-nizak)
                  </SelectItem>
                  <SelectItem value="budget_low">
                    Budžet (nizak-visok)
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {/* Offers List */}
        <div className="space-y-4">
          {filteredOffers.length === 0 ? (
            <Card>
              <CardContent className="p-8 text-center">
                <Send className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-medium mb-2">Nema ponuda</h3>
                <p className="text-muted-foreground mb-4">
                  {searchTerm || statusFilter !== 'all'
                    ? 'Nema ponuda koje odgovaraju vašim filterima.'
                    : 'Još niste poslali nijednu ponudu influencerima.'}
                </p>
                <Button asChild>
                  <Link href="/marketplace/influencers">
                    Pronađi influencere
                  </Link>
                </Button>
              </CardContent>
            </Card>
          ) : (
            filteredOffers.map(offer => (
              <Card
                key={offer.id}
                className="hover:shadow-md transition-shadow"
              >
                <CardContent className="p-6">
                  <div className="flex flex-col md:flex-row gap-4">
                    {/* Influencer info */}
                    <div className="flex items-center gap-3 md:w-64">
                      <Avatar className="h-12 w-12">
                        <AvatarImage
                          src={offer.influencer.avatar_url || ''}
                          alt={
                            offer.influencer.full_name ||
                            offer.influencer.username
                          }
                        />
                        <AvatarFallback>
                          {getInitials(offer.influencer.full_name)}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <p className="font-medium">
                          {offer.influencer.full_name ||
                            offer.influencer.username}
                        </p>
                        <p className="text-sm text-muted-foreground">
                          @{offer.influencer.username}
                        </p>
                      </div>
                    </div>

                    {/* Offer details */}
                    <div className="flex-1">
                      <div className="flex items-start justify-between mb-2">
                        <h3 className="font-semibold text-lg">{offer.title}</h3>
                        {getStatusBadge(offer.status)}
                      </div>
                      <p className="text-muted-foreground text-sm mb-3 line-clamp-2">
                        {offer.description}
                      </p>
                      <div className="flex flex-wrap gap-4 text-sm text-muted-foreground">
                        <div className="flex items-center gap-1">
                          <DollarSign className="h-4 w-4" />
                          {offer.budget.toLocaleString()} KM
                        </div>
                        <div className="flex items-center gap-1">
                          <Calendar className="h-4 w-4" />
                          {formatDistanceToNow(new Date(offer.created_at), {
                            addSuffix: true,
                            locale: hr,
                          })}
                        </div>
                        {offer.deadline && (
                          <div className="flex items-center gap-1">
                            <Clock className="h-4 w-4" />
                            Rok: {new Date(offer.deadline).toLocaleDateString()}
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Actions */}
                    <div className="flex flex-col gap-2 md:w-32">
                      <Button variant="outline" size="sm" asChild>
                        <Link href={`/dashboard/biznis/offers/${offer.id}`}>
                          <Eye className="h-4 w-4 mr-1" />
                          Detalji
                        </Link>
                      </Button>
                      {offer.status === 'accepted' && (
                        <ChatEnableButton
                          businessId={offer.business_id}
                          influencerId={offer.influencer_id}
                          offerId={offer.id}
                          userType="business"
                          variant="outline"
                          size="sm"
                          className="w-full"
                        />
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))
          )}
        </div>
      </div>
    </DashboardLayout>
  );
}
