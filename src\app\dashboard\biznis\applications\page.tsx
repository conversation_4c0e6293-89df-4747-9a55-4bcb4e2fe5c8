'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Separator } from '@/components/ui/separator';
import {
  Eye,
  Clock,
  CheckCircle,
  XCircle,
  User,
  Calendar,
  DollarSign,
} from 'lucide-react';
import Link from 'next/link';
import { getBusinessCampaignApplications } from '@/lib/campaigns';
import { DashboardLayout } from '@/components/dashboard/DashboardLayout';

interface Application {
  id: string;
  campaign_id: string;
  influencer_id: string;
  status: 'pending' | 'accepted' | 'rejected';
  proposed_rate: number;
  proposal_text: string;
  delivery_timeframe: string;
  portfolio_links: string[] | null;
  experience_relevant: string | null;
  audience_insights: string | null;
  applied_at: string;
  campaigns: {
    id: string;
    title: string;
    budget: number;
    business_id: string;
  };
  profiles: {
    id: string;
    username: string;
    full_name: string | null;
    avatar_url: string | null;
  };
}

export default function ApplicationsPage() {
  const { user } = useAuth();
  const [applications, setApplications] = useState<Application[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('all');

  useEffect(() => {
    if (!user) return;

    const loadApplications = async () => {
      try {
        const { data, error } = await getBusinessCampaignApplications(user.id);
        if (error) {
          console.error('Error loading applications:', error);
          return;
        }
        setApplications(data || []);
      } catch (error) {
        console.error('Error loading applications:', error);
      } finally {
        setLoading(false);
      }
    };

    loadApplications();
  }, [user]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'accepted':
        return 'bg-green-100 text-green-800';
      case 'rejected':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="h-4 w-4" />;
      case 'accepted':
        return <CheckCircle className="h-4 w-4" />;
      case 'rejected':
        return <XCircle className="h-4 w-4" />;
      default:
        return <Clock className="h-4 w-4" />;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending':
        return 'Na čekanju';
      case 'accepted':
        return 'Prihvaćeno';
      case 'rejected':
        return 'Odbačeno';
      default:
        return status;
    }
  };

  const filteredApplications = applications.filter(app => {
    if (activeTab === 'all') return true;
    return app.status === activeTab;
  });

  const stats = {
    total: applications.length,
    pending: applications.filter(app => app.status === 'pending').length,
    accepted: applications.filter(app => app.status === 'accepted').length,
    rejected: applications.filter(app => app.status === 'rejected').length,
  };

  if (loading) {
    return (
      <DashboardLayout>
        <div className="container mx-auto px-4 py-8">
          <div className="animate-pulse space-y-4">
            <div className="h-8 bg-gray-200 rounded w-1/4"></div>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              {[...Array(4)].map((_, i) => (
                <div key={i} className="h-24 bg-gray-200 rounded"></div>
              ))}
            </div>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Aplikacije na kampanje
          </h1>
          <p className="text-gray-600">
            Pregledajte i upravljajte aplikacijama influencera na vaše kampanje
          </p>
        </div>



        {/* Applications List */}
        <Card>
          <CardHeader>
            <CardTitle>Aplikacije</CardTitle>
            <CardDescription>
              Pregled svih aplikacija na vaše kampanje
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="grid w-full grid-cols-4">
                <TabsTrigger value="all">Sve ({stats.total})</TabsTrigger>
                <TabsTrigger value="pending">
                  Na čekanju ({stats.pending})
                </TabsTrigger>
                <TabsTrigger value="accepted">
                  Prihvaćeno ({stats.accepted})
                </TabsTrigger>
                <TabsTrigger value="rejected">
                  Odbačeno ({stats.rejected})
                </TabsTrigger>
              </TabsList>

              <TabsContent value={activeTab} className="mt-6">
                {filteredApplications.length === 0 ? (
                  <div className="text-center py-12">
                    <User className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">
                      Nema aplikacija
                    </h3>
                    <p className="text-gray-600">
                      {activeTab === 'all'
                        ? 'Još uvijek nema aplikacija na vaše kampanje.'
                        : `Nema aplikacija sa statusom "${activeTab}".`}
                    </p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {filteredApplications.map(application => (
                      <Card
                        key={application.id}
                        className="hover:shadow-md transition-shadow"
                      >
                        <CardContent className="p-6">
                          <div className="flex items-start justify-between">
                            <div className="flex items-start space-x-4 flex-1">
                              <Avatar className="h-12 w-12">
                                <AvatarImage
                                  src={application.profiles.avatar_url || ''}
                                />
                                <AvatarFallback>
                                  {(
                                    application.profiles.full_name ||
                                    application.profiles.username
                                  )
                                    .charAt(0)
                                    .toUpperCase()}
                                </AvatarFallback>
                              </Avatar>

                              <div className="flex-1 min-w-0">
                                <div className="flex items-center space-x-2 mb-2">
                                  <h3 className="text-lg font-semibold text-gray-900">
                                    {application.profiles.full_name ||
                                      application.profiles.username}
                                  </h3>
                                  <Badge variant="secondary">
                                    @{application.profiles.username}
                                  </Badge>
                                  <Badge
                                    className={getStatusColor(
                                      application.status
                                    )}
                                  >
                                    <div className="flex items-center space-x-1">
                                      {getStatusIcon(application.status)}
                                      <span>
                                        {getStatusText(application.status)}
                                      </span>
                                    </div>
                                  </Badge>
                                </div>

                                <p className="text-sm text-gray-600 mb-2">
                                  Kampanja:{' '}
                                  <span className="font-medium">
                                    {application.campaigns.title}
                                  </span>
                                </p>

                                <div className="flex items-center space-x-4 text-sm text-gray-500">
                                  <div className="flex items-center space-x-1">
                                    <DollarSign className="h-4 w-4" />
                                    <span>{application.proposed_rate} KM</span>
                                  </div>
                                  <div className="flex items-center space-x-1">
                                    <Calendar className="h-4 w-4" />
                                    <span>
                                      {application.delivery_timeframe}
                                    </span>
                                  </div>
                                </div>
                              </div>
                            </div>

                            <div className="flex items-center space-x-2">
                              <Link
                                href={`/dashboard/biznis/applications/${application.id}`}
                              >
                                <Button variant="outline" size="sm">
                                  <Eye className="h-4 w-4 mr-2" />
                                  Pregled
                                </Button>
                              </Link>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                )}
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
}
