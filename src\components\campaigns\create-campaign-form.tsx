'use client';

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import { Loader2 } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';

import { createCampaign } from '@/lib/campaigns';
import { supabase } from '@/lib/supabase';
import { useAuth } from '@/contexts/AuthContext';

// Schema za validaciju
const campaignSchema = z.object({
  // Korak 1: <PERSON><PERSON>, tipovi sad<PERSON>, budžet, naziv
  title: z
    .string()
    .min(5, 'Naziv mora imati najmanje 5 karaktera')
    .max(200, 'Naziv je predugačak'),
  budget: z
    .number()
    .min(50, 'Minimalni budžet je 50 KM')
    .max(50000, 'Maksimalni budžet je 50,000 KM'),
  
  // Korak 2: Opis kampanje
  description: z.string().min(20, 'Opis mora imati najmanje 20 karaktera'),
  
  // Korak 3: Kategorije
  selectedCategories: z
    .array(z.number())
    .min(1, 'Odaberite najmanje jednu kategoriju')
    .max(3, 'Možete odabrati maksimalno 3 kategorije'),
  
  // Korak 4: Pol i dob influencera
  gender: z.enum(['male', 'female', 'all']).optional(),
  ageRangeMin: z.number().min(13).max(65).optional(),
  ageRangeMax: z.number().min(13).max(65).optional(),
  
  // Korak 5: Obavezne poruke i zabrane
  hashtags: z.string().optional(),
  doNotMention: z.string().optional(),
  
  // Korak 6: Dodatne napomene
  additionalNotes: z.string().optional(),
  
  // Ostali podaci
  collaborationType: z.enum(['paid', 'barter', 'hybrid']).default('paid'),
  showBusinessName: z.boolean().default(false),
  isFeatured: z.boolean().default(false),
});

type CampaignForm = z.infer<typeof campaignSchema>;

// Tipovi za platforme i sadržaj
interface Platform {
  id: number;
  name: string;
  slug: string;
  icon: string;
}

interface Category {
  id: number;
  name: string;
  slug: string;
  icon: string;
}

interface PlatformContentType {
  platform: string;
  types: string[];
}

// Mapiranje platformi na dostupne tipove sadržaja
const platformContentTypes: PlatformContentType[] = [
  {
    platform: 'Instagram',
    types: ['Photo post', 'Video', 'Story']
  },
  {
    platform: 'TikTok', 
    types: ['Video']
  },
  {
    platform: 'YouTube',
    types: ['Video', 'Shorts']
  },
  {
    platform: 'Facebook',
    types: ['Photo post', 'Video', 'Story']
  }
];

// Mapiranje tipova sadržaja iz forme na enum vrednosti u bazi
const mapContentTypeToEnum = (contentType: string): string => {
  const mapping: { [key: string]: string } = {
    'Photo post': 'post',
    'Video': 'video',
    'Story': 'story',
    'Shorts': 'reel', // YouTube Shorts mapiramo na reel
  };
  return mapping[contentType] || contentType.toLowerCase();
};

interface CreateCampaignFormProps {
  initialData?: Partial<CampaignForm>;
  onSubmit?: (data: CampaignForm) => void;
  onSaveDraft?: (data: CampaignForm) => void;
  submitButtonText?: string;
  isLoading?: boolean;
}

export function CreateCampaignForm({
  initialData,
  onSubmit,
  onSaveDraft,
  submitButtonText,
  isLoading: externalLoading,
}: CreateCampaignFormProps) {
  const router = useRouter();
  const { user } = useAuth();
  
  const [isLoading, setIsLoading] = useState(false);
  const [platforms, setPlatforms] = useState<Platform[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [currentStep, setCurrentStep] = useState(1);
  
  // State za Korak 1: Platforme i tipovi sadržaja
  const [selectedPlatformData, setSelectedPlatformData] = useState<{
    [platformId: number]: {
      selected: boolean;
      contentTypes: string[];
    }
  }>({});

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
    setError,
    clearErrors,
    getValues,
  } = useForm<CampaignForm>({
    resolver: zodResolver(campaignSchema),
    defaultValues: {
      collaborationType: 'paid',
      gender: 'all',
      showBusinessName: false,
      isFeatured: false,
      selectedCategories: [],
      ...initialData,
    },
  });

  // Load platforms and categories on mount
  useEffect(() => {
    const loadData = async () => {
      try {
        const [platformsResult, categoriesResult] = await Promise.all([
          supabase.from('platforms').select('*').eq('is_active', true).order('name'),
          supabase.from('categories').select('*').order('name'),
        ]);

        if (platformsResult.data) {
          setPlatforms(platformsResult.data);
          // Initialize platform data state
          const initialPlatformData: any = {};
          platformsResult.data.forEach(platform => {
            initialPlatformData[platform.id] = {
              selected: false,
              contentTypes: []
            };
          });
          setSelectedPlatformData(initialPlatformData);
        }
        if (categoriesResult.data) setCategories(categoriesResult.data);
      } catch (error) {
        console.error('Error loading data:', error);
        toast.error('Greška pri učitavanju podataka');
      }
    };

    loadData();
  }, []);

  // Helper funkcije za rad sa platformama
  const togglePlatform = (platformId: number) => {
    setSelectedPlatformData(prev => ({
      ...prev,
      [platformId]: {
        ...prev[platformId],
        selected: !prev[platformId]?.selected,
        contentTypes: prev[platformId]?.selected ? [] : prev[platformId]?.contentTypes || []
      }
    }));
  };

  const toggleContentType = (platformId: number, contentType: string) => {
    setSelectedPlatformData(prev => {
      const currentTypes = prev[platformId]?.contentTypes || [];
      const newTypes = currentTypes.includes(contentType)
        ? currentTypes.filter(type => type !== contentType)
        : [...currentTypes, contentType];
      
      return {
        ...prev,
        [platformId]: {
          ...prev[platformId],
          contentTypes: newTypes
        }
      };
    });
  };

  const getAvailableContentTypes = (platformName: string): string[] => {
    const platformConfig = platformContentTypes.find(p => p.platform === platformName);
    return platformConfig?.types || [];
  };

  // Step validation functions
  const validateStep1 = () => {
    const values = getValues();
    const errors = [];

    // Proverava da li je odabrana najmanje jedna platforma sa tipom sadržaja
    const selectedPlatforms = Object.entries(selectedPlatformData).filter(
      ([_, data]) => data.selected && data.contentTypes.length > 0
    );
    
    if (selectedPlatforms.length === 0) {
      errors.push('Morate odabrati najmanje jednu platformu sa tipom sadržaja');
    }
    
    if (!values.budget || values.budget <= 0) {
      errors.push('Budžet mora biti veći od 0');
    }
    
    if (!values.title?.trim()) {
      errors.push('Naziv kampanje je obavezan');
    }

    if (errors.length > 0) {
      setError('root', { message: errors.join(', ') });
      return false;
    }
    return true;
  };

  const validateStep2 = () => {
    const values = getValues();
    const errors = [];

    if (!values.description?.trim()) {
      errors.push('Opis kampanje je obavezan');
    }

    if (errors.length > 0) {
      setError('root', { message: errors.join(', ') });
      return false;
    }
    return true;
  };

  const validateStep3 = () => {
    const selectedCategories = watch('selectedCategories') || [];
    const errors = [];

    if (selectedCategories.length === 0) {
      errors.push('Morate odabrati najmanje jednu kategoriju');
    }
    if (selectedCategories.length > 3) {
      errors.push('Možete odabrati maksimalno 3 kategorije');
    }

    if (errors.length > 0) {
      setError('root', { message: errors.join(', ') });
      return false;
    }
    return true;
  };

  const nextStep = () => {
    clearErrors('root');

    if (currentStep === 1 && !validateStep1()) return;
    if (currentStep === 2 && !validateStep2()) return;
    if (currentStep === 3 && !validateStep3()) return;

    setCurrentStep(prev => Math.min(prev + 1, 6)); // Sada imamo 6 koraka
  };

  const prevStep = () => {
    setCurrentStep(prev => Math.max(prev - 1, 1));
  };

  const onFormSubmit = async (data: CampaignForm) => {
    if (!user) {
      toast.error('Morate biti ulogovani da biste kreirali kampanju');
      return;
    }

    try {
      setIsLoading(true);

      // Prepare platform and content type data
      const selectedPlatforms = Object.entries(selectedPlatformData)
        .filter(([_, data]) => data.selected && data.contentTypes.length > 0)
        .map(([platformId, data]) => ({
          platformId: parseInt(platformId),
          contentTypes: data.contentTypes
        }));

      // Collect all content types for the campaign and map to enum values
      const allContentTypes = selectedPlatforms
        .flatMap(platform => platform.contentTypes)
        .map(type => mapContentTypeToEnum(type))
        .filter((type, index, array) => array.indexOf(type) === index); // Remove duplicates

      // Create campaign with all data
      const campaignData = {
        business_id: user.id,
        title: data.title,
        description: data.description,
        budget: data.budget,
        content_types: allContentTypes.length > 0 ? allContentTypes : ['video'], // Default to video if none selected
        collaboration_type: data.collaborationType,
        gender: data.gender === 'all' ? null : data.gender,
        age_range_min: data.ageRangeMin || null,
        age_range_max: data.ageRangeMax || null,
        hashtags: data.hashtags ? data.hashtags.split(',').map(h => h.trim()) : null,
        do_not_mention: data.doNotMention ? data.doNotMention.split(',').map(d => d.trim()) : null,
        additional_notes: data.additionalNotes || null,
        show_business_name: data.showBusinessName,
        is_featured: data.isFeatured,
        status: 'draft' as const,
      };

      const { data: campaign, error: campaignError } = await createCampaign(campaignData);

      if (campaignError) {
        console.error('Campaign creation error:', campaignError);
        toast.error('Greška pri kreiranju kampanje');
        return;
      }

      if (!campaign) {
        toast.error('Greška pri kreiranju kampanje');
        return;
      }

      // Save platform associations
      if (selectedPlatforms.length > 0) {
        const platformInserts = selectedPlatforms.map(platform => ({
          campaign_id: campaign.id,
          platform_id: platform.platformId,
          content_types: platform.contentTypes
        }));

        const { error: platformError } = await supabase
          .from('campaign_platforms')
          .insert(platformInserts);

        if (platformError) {
          console.error('Platform association error:', platformError);
          // Ne prekidamo proces, samo logujemo grešku
        }
      }

      toast.success('Kampanja je uspešno kreirana!');

      if (onSubmit) {
        onSubmit(data);
      } else {
        router.push(`/campaigns/${campaign.id}`);
      }
    } catch (error) {
      console.error('Error creating campaign:', error);
      toast.error('Greška pri kreiranju kampanje');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit(onFormSubmit)} className="space-y-6">
      {/* Progress indicator */}
      <div className="mb-8">
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm font-medium">Korak {currentStep} od 6</span>
          <span className="text-sm text-muted-foreground">
            {Math.round((currentStep / 6) * 100)}% završeno
          </span>
        </div>
        <div className="w-full bg-muted rounded-full h-2">
          <div
            className="bg-primary h-2 rounded-full transition-all duration-300"
            style={{ width: `${(currentStep / 6) * 100}%` }}
          />
        </div>
      </div>

      {/* Error display */}
      {errors.root && (
        <div className="bg-destructive/10 border border-destructive/20 rounded-lg p-4">
          <p className="text-sm text-destructive">{errors.root.message}</p>
        </div>
      )}

      {/* Step 1: Platforme, tipovi sadržaja, budžet i naziv */}
      {currentStep === 1 && (
        <Card>
          <CardHeader>
            <CardTitle>Korak 1: Odaberite platforme i tipove sadržaja</CardTitle>
            <CardDescription>
              Odaberite šta želite da influencer odradi? Na kojoj platformi i kroz koji tip sadržaja da Vas promoviše?
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Platforme i tipovi sadržaja */}
            <div>
              <Label className="text-base font-medium">Platforme i tipovi sadržaja *</Label>
              <p className="text-sm text-muted-foreground mb-4">
                Odaberite platforme i tipove sadržaja koje želite
              </p>
              <div className="space-y-4">
                {platforms.map(platform => {
                  const availableTypes = getAvailableContentTypes(platform.name);
                  const isSelected = selectedPlatformData[platform.id]?.selected || false;
                  const selectedTypes = selectedPlatformData[platform.id]?.contentTypes || [];

                  return (
                    <div key={platform.id} className="border rounded-lg p-4">
                      <div className="flex items-center space-x-3 mb-3">
                        <Checkbox
                          id={`platform-${platform.id}`}
                          checked={isSelected}
                          onCheckedChange={() => togglePlatform(platform.id)}
                        />
                        <Label
                          htmlFor={`platform-${platform.id}`}
                          className="flex items-center gap-2 text-base font-medium cursor-pointer"
                        >
                          <span className="text-lg">{platform.icon}</span>
                          {platform.name}
                        </Label>
                      </div>

                      {isSelected && (
                        <div className="ml-6 space-y-2">
                          <p className="text-sm text-muted-foreground">Tipovi sadržaja:</p>
                          <div className="grid grid-cols-2 gap-2">
                            {availableTypes.map(type => (
                              <div key={type} className="flex items-center space-x-2">
                                <Checkbox
                                  id={`${platform.id}-${type}`}
                                  checked={selectedTypes.includes(type)}
                                  onCheckedChange={() => toggleContentType(platform.id, type)}
                                />
                                <Label
                                  htmlFor={`${platform.id}-${type}`}
                                  className="text-sm cursor-pointer"
                                >
                                  {type}
                                </Label>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  );
                })}
              </div>
            </div>

            {/* Maksimalni iznos */}
            <div>
              <Label htmlFor="budget">Koliki je maksimalni iznos koji ste spremni platiti po influenceru? *</Label>
              <p className="text-sm text-muted-foreground mb-2">
                Ovaj iznos bit će prikazan influencerima na listi i stranici kampanje. Na Vašu kampanju se može prijaviti više influencera i Vi odlučujete koliko ćete odobriti da odrade posao za navedenu cijenu.
              </p>
              <Input
                id="budget"
                type="number"
                {...register('budget', { valueAsNumber: true })}
                placeholder="1000"
                className="max-w-xs"
              />
              {errors.budget && (
                <p className="text-sm text-destructive mt-1">
                  {errors.budget.message}
                </p>
              )}
            </div>

            {/* Naziv kampanje */}
            <div>
              <Label htmlFor="title">Naziv kampanje *</Label>
              <Input
                id="title"
                {...register('title')}
                placeholder="npr. Promotivni video za naš restoran"
              />
              {errors.title && (
                <p className="text-sm text-destructive mt-1">
                  {errors.title.message}
                </p>
              )}
            </div>

            <div className="flex justify-end">
              <Button type="button" onClick={nextStep}>
                Sledeći korak
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Step 2: Opis kampanje */}
      {currentStep === 2 && (
        <Card>
          <CardHeader>
            <CardTitle>Korak 2: Opis kampanje</CardTitle>
            <CardDescription>
              Opišite kampanju i šta očekujete od influencera
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="description">Opis kampanje *</Label>
              <p className="text-sm text-muted-foreground mb-2">
                Npr. Želimo da influencer posjeti naš restoran i snimi kratak video u kojem preporučuje naše jelo dana
              </p>
              <Textarea
                id="description"
                {...register('description')}
                placeholder="Npr. Želimo da influencer posjeti naš restoran i snimi kratak video u kojem preporučuje naše jelo dana"
                rows={6}
              />
              {errors.description && (
                <p className="text-sm text-destructive mt-1">
                  {errors.description.message}
                </p>
              )}
            </div>

            <div className="flex justify-between">
              <Button type="button" variant="outline" onClick={prevStep}>
                Prethodni korak
              </Button>
              <Button type="button" onClick={nextStep}>
                Sledeći korak
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Step 3: Kategorije sadržaja */}
      {currentStep === 3 && (
        <Card>
          <CardHeader>
            <CardTitle>Korak 3: Kategorije sadržaja</CardTitle>
            <CardDescription>
              Odaberite kategorije sadržaja koje kreira influencer (min. 1, max. 3)
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label className="text-base font-medium">Kategorije *</Label>
              <p className="text-sm text-muted-foreground mb-4">
                Odaberite 1-3 kategorije koje najbolje opisuju sadržaj koji želite
              </p>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                {categories.map(category => {
                  const selectedCategories = watch('selectedCategories') || [];
                  const isSelected = selectedCategories.includes(category.id);
                  const canSelect = selectedCategories.length < 3 || isSelected;

                  return (
                    <div
                      key={category.id}
                      className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                        isSelected
                          ? 'border-primary bg-primary/5'
                          : canSelect
                          ? 'border-border hover:border-primary/50'
                          : 'border-border opacity-50 cursor-not-allowed'
                      }`}
                      onClick={() => {
                        if (!canSelect) return;

                        const currentSelected = selectedCategories;
                        const newSelected = isSelected
                          ? currentSelected.filter(id => id !== category.id)
                          : [...currentSelected, category.id];

                        setValue('selectedCategories', newSelected);
                      }}
                    >
                      <div className="flex items-center gap-2">
                        <span className="text-lg">{category.icon}</span>
                        <span className="text-sm font-medium">{category.name}</span>
                      </div>
                    </div>
                  );
                })}
              </div>
              {errors.selectedCategories && (
                <p className="text-sm text-destructive mt-2">
                  {errors.selectedCategories.message}
                </p>
              )}
            </div>

            <div className="flex justify-between">
              <Button type="button" variant="outline" onClick={prevStep}>
                Prethodni korak
              </Button>
              <Button type="button" onClick={nextStep}>
                Sledeći korak
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Step 4: Pol i dob influencera */}
      {currentStep === 4 && (
        <Card>
          <CardHeader>
            <CardTitle>Korak 4: Pol i dob influencera</CardTitle>
            <CardDescription>
              Odaberite demografske karakteristike influencera
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Pol influencera */}
            <div>
              <Label className="text-base font-medium">Pol influencera</Label>
              <p className="text-sm text-muted-foreground mb-3">
                Odaberite pol influencera koji želite da promoviše vašu kampanju
              </p>
              <div className="grid grid-cols-3 gap-3">
                {[
                  { value: 'male', label: 'Muški' },
                  { value: 'female', label: 'Ženski' },
                  { value: 'all', label: 'Svi' }
                ].map(option => (
                  <div
                    key={option.value}
                    className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                      watch('gender') === option.value
                        ? 'border-primary bg-primary/5'
                        : 'border-border hover:border-primary/50'
                    }`}
                    onClick={() => setValue('gender', option.value as any)}
                  >
                    <div className="text-center">
                      <span className="font-medium">{option.label}</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Dob influencera */}
            <div>
              <Label className="text-base font-medium">Dob influencera</Label>
              <p className="text-sm text-muted-foreground mb-3">
                Odaberite uzrasni opseg influencera
              </p>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="ageRangeMin">Od godine</Label>
                  <Input
                    id="ageRangeMin"
                    type="number"
                    {...register('ageRangeMin', { valueAsNumber: true })}
                    placeholder="18"
                    min="13"
                    max="65"
                  />
                </div>
                <div>
                  <Label htmlFor="ageRangeMax">Do godine</Label>
                  <Input
                    id="ageRangeMax"
                    type="number"
                    {...register('ageRangeMax', { valueAsNumber: true })}
                    placeholder="35"
                    min="13"
                    max="65"
                  />
                </div>
              </div>
            </div>

            <div className="flex justify-between">
              <Button type="button" variant="outline" onClick={prevStep}>
                Prethodni korak
              </Button>
              <Button type="button" onClick={nextStep}>
                Sledeći korak
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Step 5: Obavezne poruke i zabrane */}
      {currentStep === 5 && (
        <Card>
          <CardHeader>
            <CardTitle>Korak 5: Obavezne poruke i zabrane</CardTitle>
            <CardDescription>
              Definirajte ključne fraze, hashtag-ove i zabrane za sadržaj
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Obavezni hashtag-ovi */}
            <div>
              <Label htmlFor="hashtags">Obavezni hashtag-ovi</Label>
              <p className="text-sm text-muted-foreground mb-2">
                Unesite hashtag-ove koje influencer mora koristiti (odvojite zarezom)
              </p>
              <Input
                id="hashtags"
                {...register('hashtags')}
                placeholder="npr. #novoproizvod, #promocija, #sarajevo"
              />
            </div>

            {/* Zabranjene stvari */}
            <div>
              <Label htmlFor="doNotMention">Zabranjene stvari</Label>
              <p className="text-sm text-muted-foreground mb-2">
                Navedite šta ne smije biti u sadržaju (konkurentski brendovi, određene riječi, itd.)
              </p>
              <Textarea
                id="doNotMention"
                {...register('doNotMention')}
                placeholder="npr. konkurentski brendovi, jeftino, loš kvalitet"
                rows={3}
              />
            </div>

            <div className="flex justify-between">
              <Button type="button" variant="outline" onClick={prevStep}>
                Prethodni korak
              </Button>
              <Button type="button" onClick={nextStep}>
                Sledeći korak
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Step 6: Dodatne napomene */}
      {currentStep === 6 && (
        <Card>
          <CardHeader>
            <CardTitle>Korak 6: Dodatne napomene</CardTitle>
            <CardDescription>
              Dodajte posebne smjernice, reference, ton komunikacije ili primjere dobrog sadržaja
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Dodatne napomene */}
            <div>
              <Label htmlFor="additionalNotes">Dodatne napomene</Label>
              <p className="text-sm text-muted-foreground mb-2">
                Slobodno polje gdje možete dati posebne smjernice, reference, ton komunikacije, primjere dobrog sadržaja. Nešto što bi dodatno pomoglo da influencer shvati šta se traži i kako da odradi zadatak.
              </p>
              <Textarea
                id="additionalNotes"
                {...register('additionalNotes')}
                placeholder="npr. Želimo opuštenu i prirodnu komunikaciju. Molimo da se fokusirate na kvalitet proizvoda i korisnost. Možete pogledati naše prethodne kampanje na @nasinstagram za inspiraciju."
                rows={6}
              />
            </div>

            <div className="flex justify-between">
              <Button type="button" variant="outline" onClick={prevStep}>
                Prethodni korak
              </Button>
              <Button
                type="submit"
                disabled={isLoading || externalLoading}
              >
                {(isLoading || externalLoading) && (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                )}
                Kreiraj kampanju
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </form>
  );
}
