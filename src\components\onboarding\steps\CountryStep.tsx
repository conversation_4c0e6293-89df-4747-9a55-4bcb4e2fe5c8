'use client';

import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

interface CountryStepProps {
  value: string;
  onChange: (value: string) => void;
  onNext: () => void;
  onBack: () => void;
  title?: string;
  description?: string;
}

const balkanCountries = [
  { code: 'RS', name: '<PERSON><PERSON><PERSON>' },
  { code: 'BA', name: 'Bosna i Hercegovina' },
  { code: 'HR', name: '<PERSON><PERSON><PERSON><PERSON>' },
  { code: 'ME', name: 'Crna Gora' },
  { code: 'M<PERSON>', name: '<PERSON><PERSON> Makedonija' },
  { code: 'SI', name: 'Slovenija' },
  { code: 'AL', name: 'Albani<PERSON>' },
  { code: 'BG', name: 'B<PERSON>rsk<PERSON>' },
  { code: 'RO', name: '<PERSON><PERSON><PERSON><PERSON>' },
  { code: 'G<PERSON>', name: '<PERSON><PERSON><PERSON><PERSON>' },
];

export function CountryStep({
  value,
  onChange,
  onNext,
  onBack,
  title = "Iz koje ste države?",
  description = "Ova informacija pomaže brendovima da pronađu lokalne influencere"
}: CountryStepProps) {
  const handleNext = () => {
    if (value) {
      onNext();
    }
  };

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">
          {title}
        </h2>
        <p className="text-gray-600">
          {description}
        </p>
      </div>

      <div className="space-y-4">
        <div>
          <Label htmlFor="country">Država</Label>
          <Select value={value} onValueChange={onChange}>
            <SelectTrigger>
              <SelectValue placeholder="Izaberite državu" />
            </SelectTrigger>
            <SelectContent>
              {balkanCountries.map((country) => (
                <SelectItem key={country.code} value={country.name}>
                  {country.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div className="text-sm text-gray-500">
          <p>Trenutno podržavamo influencere iz Balkanskih zemalja.</p>
        </div>
      </div>

      <div className="flex gap-3">
        <Button
          variant="outline"
          onClick={onBack}
          className="flex-1"
        >
          Nazad
        </Button>
        <Button
          onClick={handleNext}
          disabled={!value}
          className="flex-1"
        >
          Dalje
        </Button>
      </div>
    </div>
  );
}
