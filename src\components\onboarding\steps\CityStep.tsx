'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

interface CityStepProps {
  value: string;
  onChange?: (value: string) => void;
  onNext: (() => void) | ((city: string) => void);
  onBack: () => void;
  isLastStep?: boolean;
  isLoading?: boolean;
  title?: string;
  description?: string;
}

export function CityStep({
  value,
  onChange,
  onNext,
  onBack,
  isLastStep = false,
  isLoading = false,
  title = "U kom gradu živite?",
  description = "Ovo polje je opcionalno, ali pomaže brendovima da pronađu lokalne influencere"
}: CityStepProps) {
  const [localValue, setLocalValue] = useState(value);

  const handleInputChange = (newValue: string) => {
    setLocalValue(newValue);
    if (onChange) {
      onChange(newValue);
    }
  };

  const handleNext = () => {
    if (onNext.length > 0) {
      // onNext expects a parameter (business flow)
      (onNext as (city: string) => void)(localValue);
    } else {
      // onNext expects no parameters (influencer flow)
      (onNext as () => void)();
    }
  };

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">
          {title}
        </h2>
        <p className="text-gray-600">
          {description}
        </p>
      </div>

      <div className="space-y-4">
        <div>
          <Label htmlFor="city">Grad (opcionalno)</Label>
          <Input
            id="city"
            type="text"
            value={localValue}
            onChange={(e) => handleInputChange(e.target.value)}
            placeholder="npr. Beograd, Zagreb, Sarajevo..."
            maxLength={100}
          />
        </div>

        <div className="text-sm text-gray-500">
          <p>Možete preskočiti ovaj korak ako ne želite da delite lokaciju.</p>
        </div>
      </div>

      <div className="flex gap-3">
        <Button
          variant="outline"
          onClick={onBack}
          className="flex-1"
        >
          Nazad
        </Button>
        <Button
          onClick={handleNext}
          className="flex-1"
          disabled={isLoading}
        >
          {isLoading ? 'Završavam...' : (isLastStep ? 'Završi' : 'Dalje')}
        </Button>
      </div>
    </div>
  );
}
