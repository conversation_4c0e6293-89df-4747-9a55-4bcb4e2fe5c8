'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { getInfluencer } from '@/lib/profiles';
import { DashboardLayout } from '@/components/dashboard/DashboardLayout';
import { Loader2 } from 'lucide-react';

export default function InfluencerDashboardPage() {
  const router = useRouter();
  const { user } = useAuth();
  const [influencer, setInfluencer] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (user) {
      loadInfluencer();
    }
  }, [user]);

  const loadInfluencer = async () => {
    try {
      setLoading(true);
      const { data, error } = await getInfluencer(user!.id);

      if (error || !data) {
        // Influencer profile doesn't exist, redirect to profile creation
        router.push('/profil/kreiranje/influencer');
        return;
      }

      setInfluencer(data);
    } catch (err) {
      console.error('Error loading influencer data:', err);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <DashboardLayout requiredUserType="influencer">
        <div className="flex items-center justify-center h-64">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout requiredUserType="influencer">
      <div>
        <h1 className="text-3xl font-bold text-foreground">
          Dobrodošli, {influencer?.profiles?.username || 'Influencer'}!
        </h1>
      </div>
    </DashboardLayout>
  );
}
