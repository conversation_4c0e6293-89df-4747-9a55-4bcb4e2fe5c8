'use client';

import { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import {
  CheckCircle,
  XCircle,
  Clock,
  FileText,
  User,
  Calendar,
  DollarSign,
  Star,
} from 'lucide-react';
import { JobCompletionWithDetails } from '@/lib/job-completions';
import {
  approveJobCompletion,
  rejectJobCompletion,
} from '@/lib/job-completions';
import { toast } from 'sonner';

interface JobCompletionCardProps {
  jobCompletion: JobCompletionWithDetails;
  userType: 'influencer' | 'business';
  onUpdate?: () => void;
}

const statusConfig = {
  pending: {
    label: 'Na čekanju',
    color: 'bg-yellow-100 text-yellow-800',
    icon: Clock,
  },
  submitted: {
    label: 'Poslano na pregled',
    color: 'bg-blue-100 text-blue-800',
    icon: FileText,
  },
  approved: {
    label: 'Odobreno',
    color: 'bg-green-100 text-green-800',
    icon: CheckCircle,
  },
  rejected: {
    label: 'Odbačeno',
    color: 'bg-red-100 text-red-800',
    icon: XCircle,
  },
  completed: {
    label: 'Završeno',
    color: 'bg-green-100 text-green-800',
    icon: CheckCircle,
  },
};

export function JobCompletionCard({
  jobCompletion,
  userType,
  onUpdate,
}: JobCompletionCardProps) {
  const [isReviewing, setIsReviewing] = useState(false);
  const [reviewNotes, setReviewNotes] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const status = jobCompletion.status || 'pending';
  const config = statusConfig[status as keyof typeof statusConfig];
  const StatusIcon = config?.icon || Clock;

  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('bs-BA', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const renderStars = (rating: number | null) => {
    if (!rating) return null;

    return (
      <div className="flex items-center gap-1">
        {[1, 2, 3, 4, 5].map(star => (
          <Star
            key={star}
            className={`h-4 w-4 ${
              star <= rating
                ? 'fill-yellow-400 text-yellow-400'
                : 'text-gray-300'
            }`}
          />
        ))}
        <span className="ml-2 text-sm font-medium">{rating}/5</span>
      </div>
    );
  };

  const handleApprove = async () => {
    setIsLoading(true);
    try {
      const { error } = await approveJobCompletion(
        jobCompletion.id,
        reviewNotes
      );
      if (error) {
        toast.error('Failed to approve job completion');
        return;
      }
      toast.success('Job completion approved successfully');
      setIsReviewing(false);
      setReviewNotes('');
      onUpdate?.();
    } catch (error) {
      toast.error('An unexpected error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  const handleReject = async () => {
    if (!reviewNotes.trim()) {
      toast.error('Please provide a reason for rejection');
      return;
    }

    setIsLoading(true);
    try {
      const { error } = await rejectJobCompletion(
        jobCompletion.id,
        reviewNotes
      );
      if (error) {
        toast.error('Failed to reject job completion');
        return;
      }
      toast.success('Job completion rejected');
      setIsReviewing(false);
      setReviewNotes('');
      onUpdate?.();
    } catch (error) {
      toast.error('An unexpected error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  const canReview = userType === 'business' && status === 'submitted';

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-start justify-between">
          <div className="space-y-2">
            <CardTitle className="text-lg">
              {jobCompletion.direct_offer?.title ||
                jobCompletion.campaign_application?.campaign?.title ||
                'Posao'}
            </CardTitle>
            <div className="flex items-center gap-2">
              <StatusIcon className="h-4 w-4" />
              <Badge className={config?.color}>{config?.label}</Badge>
            </div>
          </div>
          <div className="text-right">
            <div className="flex items-center gap-1 text-sm text-muted-foreground">
              <DollarSign className="h-4 w-4" />$
              {jobCompletion.direct_offer?.budget ||
                jobCompletion.campaign_application?.proposed_rate ||
                0}
            </div>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Job Details */}
        <div className="space-y-2">
          <h4 className="font-medium text-sm">
            {jobCompletion.direct_offer ? 'Detalji ponude' : 'Detalji kampanje'}
          </h4>
          <p className="text-sm text-muted-foreground">
            {jobCompletion.direct_offer?.description ||
              jobCompletion.campaign_application?.campaign?.description ||
              'Nema opisa'}
          </p>
        </div>

        {/* Participants */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label className="text-sm font-medium">Influencer</Label>
            <div className="flex items-center gap-2">
              <Avatar className="h-8 w-8">
                <AvatarImage
                  src={jobCompletion.influencer_profile?.avatar_url || ''}
                />
                <AvatarFallback>
                  <User className="h-4 w-4" />
                </AvatarFallback>
              </Avatar>
              <div>
                <p className="text-sm font-medium">
                  {jobCompletion.influencer_profile?.full_name || 'Nepoznato'}
                </p>
                <p className="text-xs text-muted-foreground">
                  @{jobCompletion.influencer_profile?.username}
                </p>
              </div>
            </div>
          </div>

          <div className="space-y-2">
            <Label className="text-sm font-medium">Biznis</Label>
            <div className="flex items-center gap-2">
              <Avatar className="h-8 w-8">
                <AvatarImage
                  src={jobCompletion.business_profile?.avatar_url || ''}
                />
                <AvatarFallback>
                  <User className="h-4 w-4" />
                </AvatarFallback>
              </Avatar>
              <div>
                <p className="text-sm font-medium">
                  {jobCompletion.business_profile?.full_name || 'Nepoznato'}
                </p>
                <p className="text-xs text-muted-foreground">
                  @{jobCompletion.business_profile?.username}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Submission Details */}
        {jobCompletion.submission_notes && (
          <div className="space-y-2">
            <Label className="text-sm font-medium">Napomene o predaji</Label>
            <div className="bg-muted p-3 rounded-md">
              <p className="text-sm">{jobCompletion.submission_notes}</p>
            </div>
          </div>
        )}

        {/* Review Notes */}
        {jobCompletion.review_notes && (
          <div className="space-y-2">
            <Label className="text-sm font-medium">Napomene o pregledu</Label>
            <div className="bg-muted p-3 rounded-md">
              <p className="text-sm">{jobCompletion.review_notes}</p>
            </div>
          </div>
        )}

        {/* Timestamps */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-xs text-muted-foreground">
          <div className="flex items-center gap-1">
            <Calendar className="h-3 w-3" />
            <span>Kreiran: {formatDate(jobCompletion.created_at)}</span>
          </div>
          {jobCompletion.submitted_at && (
            <div className="flex items-center gap-1">
              <Calendar className="h-3 w-3" />
              <span>Poslan: {formatDate(jobCompletion.submitted_at)}</span>
            </div>
          )}
          {jobCompletion.reviewed_at && (
            <div className="flex items-center gap-1">
              <Calendar className="h-3 w-3" />
              <span>Pregledan: {formatDate(jobCompletion.reviewed_at)}</span>
            </div>
          )}
        </div>

        {/* Ocjene */}
        {(jobCompletion.business_to_influencer_review ||
          jobCompletion.influencer_to_business_review) && (
          <div className="space-y-4 pt-4 border-t">
            <h4 className="font-medium text-sm">Ocjene</h4>

            {/* Ocjena biznisa za influencera */}
            {jobCompletion.business_to_influencer_review && (
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label className="text-sm font-medium">
                    Ocjena biznisa za influencera
                  </Label>
                  {renderStars(
                    jobCompletion.business_to_influencer_review.rating
                  )}
                </div>
                {jobCompletion.business_to_influencer_review.comment && (
                  <div className="bg-muted p-3 rounded-md">
                    <p className="text-sm">
                      {jobCompletion.business_to_influencer_review.comment}
                    </p>
                  </div>
                )}
                <p className="text-xs text-muted-foreground">
                  {formatDate(
                    jobCompletion.business_to_influencer_review.created_at
                  )}
                </p>
              </div>
            )}

            {/* Ocjena influencera za biznis */}
            {jobCompletion.influencer_to_business_review && (
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label className="text-sm font-medium">
                    Ocjena influencera za biznis
                  </Label>
                  {renderStars(
                    jobCompletion.influencer_to_business_review.rating
                  )}
                </div>
                {jobCompletion.influencer_to_business_review.comment && (
                  <div className="bg-muted p-3 rounded-md">
                    <p className="text-sm">
                      {jobCompletion.influencer_to_business_review.comment}
                    </p>
                  </div>
                )}
                <p className="text-xs text-muted-foreground">
                  {formatDate(
                    jobCompletion.influencer_to_business_review.created_at
                  )}
                </p>
              </div>
            )}
          </div>
        )}

        {/* Review Actions */}
        {canReview && (
          <div className="space-y-4 pt-4 border-t">
            {!isReviewing ? (
              <Button onClick={() => setIsReviewing(true)} className="w-full">
                Pregljedaj predaju
              </Button>
            ) : (
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="review-notes">
                    Napomene o pregledu (opcionalno za odobravanje, obavezno za
                    odbacivanje)
                  </Label>
                  <Textarea
                    id="review-notes"
                    placeholder="Dodajte napomene o pregledu..."
                    value={reviewNotes}
                    onChange={e => setReviewNotes(e.target.value)}
                    rows={3}
                  />
                </div>
                <div className="flex gap-2">
                  <Button
                    onClick={handleApprove}
                    disabled={isLoading}
                    className="flex-1"
                    variant="default"
                  >
                    <CheckCircle className="h-4 w-4 mr-2" />
                    Odobri
                  </Button>
                  <Button
                    onClick={handleReject}
                    disabled={isLoading}
                    className="flex-1"
                    variant="destructive"
                  >
                    <XCircle className="h-4 w-4 mr-2" />
                    Odbaci
                  </Button>
                  <Button
                    onClick={() => {
                      setIsReviewing(false);
                      setReviewNotes('');
                    }}
                    disabled={isLoading}
                    variant="outline"
                  >
                    Otkaži
                  </Button>
                </div>
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
