'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { getBusiness } from '@/lib/profiles';
import { DashboardLayout } from '@/components/dashboard/DashboardLayout';
import { Loader2 } from 'lucide-react';

export default function BiznisDashboardPage() {
  const router = useRouter();
  const { user } = useAuth();
  const [business, setBusiness] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (user) {
      loadBusiness();
    }
  }, [user]);

  const loadBusiness = async () => {
    try {
      setLoading(true);
      const { data, error } = await getBusiness(user!.id);

      if (error || !data) {
        // Business profile doesn't exist, redirect to profile creation
        router.push('/profil/kreiranje/biznis');
        return;
      }

      setBusiness(data);
    } catch (err) {
      console.error('Error loading business data:', err);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <DashboardLayout requiredUserType="business">
        <div className="flex items-center justify-center h-64">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout requiredUserType="business">
      <div>
        <h1 className="text-3xl font-bold text-foreground">
          Dobrodošli, {business?.company_name || business?.profiles?.username || 'Biznis'}!
        </h1>
      </div>
    </DashboardLayout>
  );
}
