'use client';

import { useEffect, useState } from 'react';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import { DashboardLayout } from '@/components/dashboard/DashboardLayout';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Separator } from '@/components/ui/separator';
import {
  ArrowLeft,
  Clock,
  CheckCircle,
  XCircle,
  Calendar,
  DollarSign,
  MessageCircle,
  User,
  Mail,
  Phone,
  MapPin,
  FileText,
  Star,
} from 'lucide-react';
import { getDirectOffer, type DirectOfferWithDetails } from '@/lib/offers';
import { formatDistanceToNow } from 'date-fns';
import { hr } from 'date-fns/locale';
import Link from 'next/link';
import { ChatPermissionStatus } from '@/components/chat/ChatPermissionStatus';
import { ChatEnableButton } from '@/components/chat/ChatEnableButton';
import {
  getJobCompletionByDirectOffer,
  type JobCompletion,
} from '@/lib/job-completions';
import { toast } from 'sonner';
import { ApproveJobModal } from '@/components/job-completion/ApproveJobModal';
import { RejectJobModal } from '@/components/job-completion/RejectJobModal';

export default function OfferDetailsPage() {
  const params = useParams();
  const router = useRouter();
  const [offer, setOffer] = useState<DirectOfferWithDetails | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [jobCompletion, setJobCompletion] = useState<JobCompletion | null>(
    null
  );
  const [isLoadingJobCompletion, setIsLoadingJobCompletion] = useState(false);
  const [showApproveModal, setShowApproveModal] = useState(false);
  const [showRejectModal, setShowRejectModal] = useState(false);

  useEffect(() => {
    if (params.id) {
      loadOffer(params.id as string);
      loadJobCompletion(params.id as string);
    }
  }, [params.id]);

  const loadOffer = async (offerId: string) => {
    setIsLoading(true);
    try {
      const { data, error } = await getDirectOffer(offerId);
      if (error) {
        console.error('Error loading offer:', error);
        router.push('/dashboard/biznis/offers');
      } else {
        setOffer(data);
      }
    } catch (error) {
      console.error('Error loading offer:', error);
      router.push('/dashboard/biznis/offers');
    } finally {
      setIsLoading(false);
    }
  };

  const loadJobCompletion = async (offerId: string) => {
    setIsLoadingJobCompletion(true);
    try {
      const { data, error } = await getJobCompletionByDirectOffer(offerId);
      if (error) {
        console.error('Error loading job completion:', error);
      } else {
        setJobCompletion(data);
      }
    } catch (error) {
      console.error('Error loading job completion:', error);
    } finally {
      setIsLoadingJobCompletion(false);
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return (
          <Badge
            variant="outline"
            className="text-yellow-600 border-yellow-600"
          >
            <Clock className="w-3 h-3 mr-1" />
            Na čekanju
          </Badge>
        );
      case 'accepted':
        return (
          <Badge variant="outline" className="text-green-600 border-green-600">
            <CheckCircle className="w-3 h-3 mr-1" />
            Prihvaćeno
          </Badge>
        );
      case 'rejected':
        return (
          <Badge variant="outline" className="text-red-600 border-red-600">
            <XCircle className="w-3 h-3 mr-1" />
            Odbijeno
          </Badge>
        );
      case 'completed':
        return (
          <Badge variant="outline" className="text-blue-600 border-blue-600">
            <CheckCircle className="w-3 h-3 mr-1" />
            Završeno
          </Badge>
        );
      case 'cancelled':
        return (
          <Badge variant="outline" className="text-gray-600 border-gray-600">
            <XCircle className="w-3 h-3 mr-1" />
            Otkazano
          </Badge>
        );
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const getInitials = (name: string | null) => {
    if (!name) return 'U';
    return name
      .split(' ')
      .map(word => word[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  if (isLoading) {
    return (
      <DashboardLayout requiredUserType="business">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      </DashboardLayout>
    );
  }

  if (!offer) {
    return (
      <DashboardLayout requiredUserType="business">
        <div className="text-center py-12">
          <h2 className="text-2xl font-bold mb-4">Ponuda nije pronađena</h2>
          <Button asChild>
            <Link href="/dashboard/biznis/offers">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Nazad na ponude
            </Link>
          </Button>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout requiredUserType="business">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center gap-4">
          <Button variant="outline" size="sm" asChild>
            <Link href="/dashboard/biznis/offers">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Nazad
            </Link>
          </Button>
          <div className="flex-1">
            <h1 className="text-2xl md:text-3xl font-bold">{offer.title}</h1>
            <p className="text-muted-foreground mt-1">
              Detalji ponude poslane{' '}
              {formatDistanceToNow(new Date(offer.created_at), {
                addSuffix: true,
                locale: hr,
              })}
            </p>
          </div>
          {getStatusBadge(offer.status)}
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Offer Details */}
            <Card>
              <CardHeader>
                <CardTitle>Detalji ponude</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h4 className="font-medium mb-2">Opis projekta</h4>
                  <p className="text-muted-foreground">{offer.description}</p>
                </div>

                <Separator />

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <h4 className="font-medium mb-2">Budžet</h4>
                    <div className="flex items-center gap-2">
                      <DollarSign className="h-4 w-4 text-muted-foreground" />
                      <span className="text-lg font-semibold">
                        {offer.budget.toLocaleString()} KM
                      </span>
                    </div>
                  </div>
                  {offer.deadline && (
                    <div>
                      <h4 className="font-medium mb-2">Rok za završetak</h4>
                      <div className="flex items-center gap-2">
                        <Calendar className="h-4 w-4 text-muted-foreground" />
                        <span>
                          {new Date(offer.deadline).toLocaleDateString()}
                        </span>
                      </div>
                    </div>
                  )}
                </div>

                <Separator />

                <div>
                  <h4 className="font-medium mb-2">Tipovi sadržaja</h4>
                  <div className="flex flex-wrap gap-2">
                    {offer.content_types.map((type, index) => (
                      <Badge key={index} variant="secondary">
                        {type}
                      </Badge>
                    ))}
                  </div>
                </div>

                <div>
                  <h4 className="font-medium mb-2">Platforme</h4>
                  <div className="flex flex-wrap gap-2">
                    {offer.platforms.map((platform, index) => (
                      <Badge key={index} variant="outline">
                        {platform}
                      </Badge>
                    ))}
                  </div>
                </div>

                {offer.requirements && (
                  <>
                    <Separator />
                    <div>
                      <h4 className="font-medium mb-2">Specifični zahtjevi</h4>
                      <p className="text-muted-foreground">
                        {offer.requirements}
                      </p>
                    </div>
                  </>
                )}

                {offer.deliverables && (
                  <>
                    <Separator />
                    <div>
                      <h4 className="font-medium mb-2">Očekivani rezultati</h4>
                      <p className="text-muted-foreground">
                        {offer.deliverables}
                      </p>
                    </div>
                  </>
                )}

                <Separator />

                <div>
                  <h4 className="font-medium mb-2">Vaša poruka</h4>
                  <p className="text-muted-foreground">
                    {offer.business_message}
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* Influencer Response */}
            {offer.influencer_response && (
              <Card>
                <CardHeader>
                  <CardTitle>Odgovor influencera</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground">
                    {offer.influencer_response}
                  </p>
                  <div className="mt-4 text-sm text-muted-foreground">
                    {offer.status === 'accepted' && offer.accepted_at && (
                      <p>
                        Prihvaćeno{' '}
                        {formatDistanceToNow(new Date(offer.accepted_at), {
                          addSuffix: true,
                          locale: hr,
                        })}
                      </p>
                    )}
                    {offer.status === 'rejected' && offer.rejected_at && (
                      <p>
                        Odbijeno{' '}
                        {formatDistanceToNow(new Date(offer.rejected_at), {
                          addSuffix: true,
                          locale: hr,
                        })}
                      </p>
                    )}
                  </div>
                </CardContent>
              </Card>
            )}
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Influencer Info */}
            <Card>
              <CardHeader>
                <CardTitle>Influencer</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center gap-3 mb-4">
                  <Avatar className="h-16 w-16">
                    <AvatarImage
                      src={offer.influencer?.avatar_url || ''}
                      alt={
                        offer.influencer?.full_name ||
                        offer.influencer?.username
                      }
                    />
                    <AvatarFallback className="text-lg">
                      {getInitials(offer.influencer?.full_name)}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <h3 className="font-semibold text-lg">
                      {offer.influencer?.full_name ||
                        offer.influencer?.username}
                    </h3>
                    <p className="text-muted-foreground">
                      @{offer.influencer?.username}
                    </p>
                  </div>
                </div>

                {offer.influencer.bio && (
                  <p className="text-sm text-muted-foreground mb-4">
                    {offer.influencer.bio}
                  </p>
                )}

                <div className="space-y-2">
                  <Button
                    variant="outline"
                    size="sm"
                    className="w-full"
                    asChild
                  >
                    <Link href={`/influencer/${offer.influencer.username}`}>
                      <User className="mr-2 h-4 w-4" />
                      Pogledaj profil
                    </Link>
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Chat Permission Status */}
            {offer.status === 'accepted' && (
              <div className="space-y-4">
                <ChatPermissionStatus
                  businessId={offer.business_id}
                  influencerId={offer.influencer_id}
                  offerId={offer.id}
                  userType="business"
                  onChatEnabled={() => {
                    console.log('Chat enabled for offer:', offer.id);
                  }}
                />

                {/* Chat Button */}
                <Card>
                  <CardContent className="p-4">
                    <ChatEnableButton
                      businessId={offer.business_id}
                      influencerId={offer.influencer_id}
                      offerId={offer.id}
                      userType="business"
                      className="w-full"
                    />
                  </CardContent>
                </Card>
              </div>
            )}

            {/* Job Completion Section */}
            {offer.status === 'accepted' && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <FileText className="h-5 w-5" />
                    Završetak posla
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {isLoadingJobCompletion ? (
                    <div className="flex items-center justify-center p-8">
                      <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
                    </div>
                  ) : jobCompletion ? (
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <Badge
                            variant={
                              jobCompletion.status === 'submitted'
                                ? 'secondary'
                                : jobCompletion.status === 'approved'
                                  ? 'default'
                                  : jobCompletion.status === 'rejected'
                                    ? 'destructive'
                                    : 'outline'
                            }
                          >
                            {jobCompletion.status === 'submitted' &&
                              'Poslano na pregled'}
                            {jobCompletion.status === 'approved' && 'Odobreno'}
                            {jobCompletion.status === 'rejected' && 'Odbačeno'}
                            {jobCompletion.status === 'pending' && 'Na čekanju'}
                          </Badge>
                        </div>
                        {jobCompletion.submitted_at && (
                          <span className="text-sm text-muted-foreground">
                            Poslano{' '}
                            {formatDistanceToNow(
                              new Date(jobCompletion.submitted_at),
                              { addSuffix: true, locale: hr }
                            )}
                          </span>
                        )}
                      </div>

                      {jobCompletion.submission_notes && (
                        <div>
                          <h4 className="font-medium mb-2">
                            Napomene influencera:
                          </h4>
                          <p className="text-sm text-muted-foreground bg-muted p-3 rounded-md">
                            {jobCompletion.submission_notes}
                          </p>
                        </div>
                      )}

                      {jobCompletion.submission_files && (
                        <div>
                          <h4 className="font-medium mb-2">Prilozi:</h4>
                          <div className="text-sm text-muted-foreground">
                            {JSON.parse(jobCompletion.submission_files).length}{' '}
                            fajl(ova) priloženo
                          </div>
                        </div>
                      )}

                      {jobCompletion.status === 'submitted' && (
                        <div className="flex gap-2 pt-4">
                          <Button
                            onClick={() => setShowApproveModal(true)}
                            className="flex-1"
                          >
                            <CheckCircle className="h-4 w-4 mr-2" />
                            Odobri rad
                          </Button>
                          <Button
                            variant="outline"
                            onClick={() => setShowRejectModal(true)}
                            className="flex-1"
                          >
                            <XCircle className="h-4 w-4 mr-2" />
                            Odbaci rad
                          </Button>
                        </div>
                      )}

                      {jobCompletion.business_notes && (
                        <div>
                          <h4 className="font-medium mb-2">Vaše napomene:</h4>
                          <p className="text-sm text-muted-foreground bg-muted p-3 rounded-md">
                            {jobCompletion.business_notes}
                          </p>
                        </div>
                      )}
                    </div>
                  ) : (
                    <div className="text-center py-8">
                      <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                      <p className="text-muted-foreground">
                        Influencer još uvijek nije poslao završetak posla
                      </p>
                    </div>
                  )}
                </CardContent>
              </Card>
            )}

            {/* Timeline */}
            <Card>
              <CardHeader>
                <CardTitle>Istorija</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                    <div>
                      <p className="text-sm font-medium">Ponuda poslana</p>
                      <p className="text-xs text-muted-foreground">
                        {formatDistanceToNow(new Date(offer.created_at), {
                          addSuffix: true,
                          locale: hr,
                        })}
                      </p>
                    </div>
                  </div>

                  {offer.status === 'accepted' && offer.accepted_at && (
                    <div className="flex items-start gap-3">
                      <div className="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
                      <div>
                        <p className="text-sm font-medium">Ponuda prihvaćena</p>
                        <p className="text-xs text-muted-foreground">
                          {formatDistanceToNow(new Date(offer.accepted_at), {
                            addSuffix: true,
                            locale: hr,
                          })}
                        </p>
                      </div>
                    </div>
                  )}

                  {offer.status === 'rejected' && offer.rejected_at && (
                    <div className="flex items-start gap-3">
                      <div className="w-2 h-2 bg-red-500 rounded-full mt-2"></div>
                      <div>
                        <p className="text-sm font-medium">Ponuda odbijena</p>
                        <p className="text-xs text-muted-foreground">
                          {formatDistanceToNow(new Date(offer.rejected_at), {
                            addSuffix: true,
                            locale: hr,
                          })}
                        </p>
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      {/* Modals */}
      {jobCompletion && (
        <>
          <ApproveJobModal
            isOpen={showApproveModal}
            onClose={() => setShowApproveModal(false)}
            onSuccess={() => {
              loadJobCompletion(params.id as string);
              setShowApproveModal(false);
            }}
            jobCompletionId={jobCompletion.id}
            influencerName={
              offer?.profiles?.full_name ||
              offer?.profiles?.username ||
              'Influencer'
            }
          />

          <RejectJobModal
            isOpen={showRejectModal}
            onClose={() => setShowRejectModal(false)}
            onSuccess={() => {
              loadJobCompletion(params.id as string);
              setShowRejectModal(false);
            }}
            jobCompletionId={jobCompletion.id}
            influencerName={
              offer?.profiles?.full_name ||
              offer?.profiles?.username ||
              'Influencer'
            }
          />
        </>
      )}
    </DashboardLayout>
  );
}
