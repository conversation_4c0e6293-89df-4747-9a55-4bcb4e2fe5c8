'use client';

import { useState } from 'react';
import { Check, ChevronsUpDown } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from '@/components/ui/command';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';

const BALKAN_COUNTRIES = [
  { value: 'albania', label: 'Albanija', flag: '🇦🇱' },
  { value: 'bosnia-herzegovina', label: 'Bosna i Hercegovina', flag: '🇧🇦' },
  { value: 'bulgaria', label: '<PERSON>ugarska', flag: '🇧🇬' },
  { value: 'montenegro', label: 'Crna Gora', flag: '🇲🇪' },
  { value: 'greece', label: 'Grčka', flag: '🇬🇷' },
  { value: 'croatia', label: 'Hrvatska', flag: '🇭🇷' },
  { value: 'kosovo', label: 'Kosovo', flag: '🇽🇰' },
  { value: 'north-macedonia', label: '<PERSON><PERSON><PERSON><PERSON>', flag: '🇲🇰' },
  { value: 'romania', label: 'Rumuni<PERSON>', flag: '🇷🇴' },
  { value: 'serbia', label: 'Srbija', flag: '🇷🇸' },
  { value: 'slovenia', label: 'Slovenija', flag: '🇸🇮' },
];

interface CountrySelectorProps {
  value?: string;
  onValueChange: (value: string) => void;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
}

export function CountrySelector({
  value,
  onValueChange,
  placeholder = 'Izaberite državu...',
  className,
  disabled = false,
}: CountrySelectorProps) {
  const [open, setOpen] = useState(false);

  const selectedCountry = BALKAN_COUNTRIES.find(
    (country) => country.value === value
  );

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className={cn(
            'w-full justify-between',
            !value && 'text-muted-foreground',
            className
          )}
          disabled={disabled}
        >
          {selectedCountry ? (
            <div className="flex items-center space-x-2">
              <span>{selectedCountry.flag}</span>
              <span>{selectedCountry.label}</span>
            </div>
          ) : (
            placeholder
          )}
          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-full p-0" align="start">
        <Command>
          <CommandInput placeholder="Pretražite države..." />
          <CommandEmpty>Nema rezultata.</CommandEmpty>
          <CommandGroup>
            {BALKAN_COUNTRIES.map((country) => (
              <CommandItem
                key={country.value}
                value={country.label}
                onSelect={() => {
                  onValueChange(country.value === value ? '' : country.value);
                  setOpen(false);
                }}
              >
                <div className="flex items-center space-x-2">
                  <span>{country.flag}</span>
                  <span>{country.label}</span>
                </div>
                <Check
                  className={cn(
                    'ml-auto h-4 w-4',
                    value === country.value ? 'opacity-100' : 'opacity-0'
                  )}
                />
              </CommandItem>
            ))}
          </CommandGroup>
        </Command>
      </PopoverContent>
    </Popover>
  );
}
