'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import {
  searchCampaigns,
  getFeaturedCampaigns,
  CampaignFilters,
} from '@/lib/campaigns';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Separator } from '@/components/ui/separator';
import {
  Search,
  Filter,
  SortAsc,
  SortDesc,
  Loader2,
  MapPin,
  Calendar,
  DollarSign,
  Users,
  Eye,
  MessageCircle,
  Star,
  TrendingUp,
} from 'lucide-react';
import { CampaignFiltersComponent } from '@/components/campaigns/campaign-filters';
import { formatDistanceToNow } from 'date-fns';
import { bs } from 'date-fns/locale';
import { DashboardLayout } from '@/components/dashboard/DashboardLayout';

interface Campaign {
  id: string;
  title: string;
  description: string;
  budget: number;
  status: string;
  location: string | null;
  application_deadline: string | null;
  min_followers: number | null;
  max_followers: number | null;
  age_range_min: number | null;
  age_range_max: number | null;
  gender: string | null;
  is_featured: boolean;
  created_at: string;
  business_id: string;
  content_types: string[] | null;
  // Optional joined data
  businesses?: {
    company_name: string;
    industry: string;
  };
}

export default function CampaignsMarketplacePage() {
  const router = useRouter();
  const { user, loading: authLoading } = useAuth();
  const [campaigns, setCampaigns] = useState<Campaign[]>([]);
  const [featuredCampaigns, setFeaturedCampaigns] = useState<Campaign[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [filters, setFilters] = useState<CampaignFilters>({});
  const [sortBy, setSortBy] = useState<
    'created_at' | 'budget' | 'application_deadline' | 'applications_count'
  >('created_at');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [showFilters, setShowFilters] = useState(false);

  // Redirect if not authenticated
  useEffect(() => {
    if (!authLoading && !user) {
      router.push('/prijava');
      return;
    }
  }, [user, authLoading, router]);

  // Load campaigns
  useEffect(() => {
    if (user) {
      loadCampaigns();
      loadFeaturedCampaigns();
    }
  }, [user, searchQuery, filters, sortBy, sortOrder]);

  const loadCampaigns = async () => {
    try {
      setLoading(true);
      const { data, error } = await searchCampaigns({
        search: searchQuery || undefined,
        ...filters,
        sortBy,
        sortOrder,
        limit: 20,
      });

      if (error) {
        console.error('Error loading campaigns:', error);
        return;
      }

      setCampaigns(data || []);
    } catch (error) {
      console.error('Error loading campaigns:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadFeaturedCampaigns = async () => {
    try {
      const { data, error } = await getFeaturedCampaigns(6);
      if (data) setFeaturedCampaigns(data);
    } catch (error) {
      console.error('Error loading featured campaigns:', error);
    }
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    loadCampaigns();
  };

  const handleFiltersChange = (newFilters: CampaignFilters) => {
    setFilters(newFilters);
  };

  const toggleSort = () => {
    setSortOrder(prev => (prev === 'asc' ? 'desc' : 'asc'));
  };

  const getStatusBadge = (status: string) => {
    const statusMap = {
      active: { label: 'Aktivna', variant: 'default' as const },
      paused: { label: 'Pauzirana', variant: 'outline' as const },
    };

    const statusInfo = statusMap[status as keyof typeof statusMap];
    return statusInfo ? (
      <Badge variant={statusInfo.variant}>{statusInfo.label}</Badge>
    ) : null;
  };

  const getCollaborationTypeLabel = (type: string) => {
    const typeMap = {
      paid: 'Plaćena',
      barter: 'Barter',
      hybrid: 'Hibridna',
    };
    return typeMap[type as keyof typeof typeMap] || type;
  };

  if (authLoading) {
    return (
      <DashboardLayout requiredUserType="influencer">
        <div className="flex items-center justify-center min-h-[400px]">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      </DashboardLayout>
    );
  }

  if (!user) {
    return null;
  }

  return (
    <DashboardLayout requiredUserType="influencer">
      <div className="space-y-6">
        {/* Header */}
        <div>
          <h1 className="text-2xl md:text-3xl font-bold text-foreground">
            Kampanje
          </h1>
          <p className="text-muted-foreground mt-1">
            Pronađite savršene kampanje za vašu publiku
          </p>
        </div>

        {/* Search and Sort */}
        <div className="flex flex-col sm:flex-row gap-4">
          <form
            onSubmit={handleSearch}
            className="flex items-center gap-2 flex-1"
          >
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Pretraži kampanje..."
                value={searchQuery}
                onChange={e => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
            <Button type="submit" variant="outline" size="sm">
              <Search className="h-4 w-4" />
            </Button>
          </form>

          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowFilters(!showFilters)}
            >
              <Filter className="h-4 w-4 mr-2" />
              Filteri
            </Button>

            <Button variant="outline" size="sm" onClick={toggleSort}>
              {sortOrder === 'asc' ? (
                <SortAsc className="h-4 w-4" />
              ) : (
                <SortDesc className="h-4 w-4" />
              )}
            </Button>
          </div>
        </div>

        <div className="flex gap-8">
          {/* Filters Sidebar */}
          {showFilters && (
            <div className="w-80 flex-shrink-0">
              <CampaignFiltersComponent
                filters={filters}
                onFiltersChange={handleFiltersChange}
              />
            </div>
          )}

          {/* Main Content */}
          <div className="flex-1 space-y-8">
            {/* Featured Campaigns */}
            {featuredCampaigns.length > 0 && (
              <div>
                <div className="flex items-center gap-2 mb-4">
                  <Star className="h-5 w-5 text-yellow-500" />
                  <h2 className="text-xl font-semibold">Istaknute kampanje</h2>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {featuredCampaigns.map(campaign => (
                    <CampaignCard
                      key={campaign.id}
                      campaign={campaign}
                      featured
                    />
                  ))}
                </div>
                <Separator className="my-8" />
              </div>
            )}

            {/* All Campaigns */}
            <div>
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-semibold">Sve kampanje</h2>
                <div className="text-sm text-muted-foreground">
                  {campaigns.length} kampanja pronađeno
                </div>
              </div>

              {loading ? (
                <div className="flex items-center justify-center py-12">
                  <Loader2 className="h-8 w-8 animate-spin" />
                </div>
              ) : campaigns.length === 0 ? (
                <Card>
                  <CardContent className="py-12 text-center">
                    <TrendingUp className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <h3 className="text-lg font-medium mb-2">Nema kampanja</h3>
                    <p className="text-muted-foreground">
                      Trenutno nema kampanja koje odgovaraju vašim kriterijima.
                    </p>
                  </CardContent>
                </Card>
              ) : (
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {campaigns.map(campaign => (
                    <CampaignCard key={campaign.id} campaign={campaign} />
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}

interface CampaignCardProps {
  campaign: Campaign;
  featured?: boolean;
}

function CampaignCard({ campaign, featured = false }: CampaignCardProps) {
  const router = useRouter();

  const handleClick = () => {
    router.push(`/campaigns/${campaign.id}`);
  };

  return (
    <Card
      className={`cursor-pointer transition-all hover:shadow-md ${
        featured ? 'border-yellow-200 bg-yellow-50/50' : ''
      }`}
      onClick={handleClick}
    >
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <div className="flex items-center gap-2 mb-2">
              {featured && <Star className="h-4 w-4 text-yellow-500" />}
              <CardTitle className="text-lg line-clamp-1">
                {campaign.title}
              </CardTitle>
            </div>
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <Avatar className="h-6 w-6">
                <AvatarFallback className="text-xs">B</AvatarFallback>
              </Avatar>
              <span>{campaign.businesses?.company_name || 'Biznis'}</span>
            </div>
          </div>
          <div className="flex flex-col items-end gap-1">
            <Badge
              variant={campaign.status === 'active' ? 'default' : 'secondary'}
              className="text-xs"
            >
              {campaign.status === 'active' ? 'Aktivna' : campaign.status}
            </Badge>
            <div className="text-lg font-bold text-primary">
              {campaign.budget.toLocaleString()} KM
            </div>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        <p className="text-sm text-muted-foreground line-clamp-2">
          {campaign.description}
        </p>

        {/* Content Types */}
        {campaign.content_types && campaign.content_types.length > 0 && (
          <div className="flex flex-wrap gap-1">
            {campaign.content_types.slice(0, 3).map(contentType => (
              <Badge
                key={contentType}
                variant="outline"
                className="text-xs capitalize"
              >
                {contentType}
              </Badge>
            ))}
            {campaign.content_types.length > 3 && (
              <Badge variant="outline" className="text-xs">
                +{campaign.content_types.length - 3}
              </Badge>
            )}
          </div>
        )}

        {/* Meta info */}
        <div className="flex items-center justify-between text-xs text-muted-foreground">
          <div className="flex items-center gap-3">
            {campaign.location && (
              <span className="flex items-center gap-1">
                <MapPin className="h-3 w-3" />
                {campaign.location}
              </span>
            )}
            {campaign.application_deadline && (
              <span className="flex items-center gap-1">
                <Calendar className="h-3 w-3" />
                {new Date(campaign.application_deadline).toLocaleDateString(
                  'bs-BA'
                )}
              </span>
            )}
          </div>
          <div className="flex items-center gap-3">
            <span className="flex items-center gap-1">
              <DollarSign className="h-3 w-3" />
              {campaign.budget.toLocaleString()} KM
            </span>
          </div>
        </div>

        {/* Time ago */}
        <div className="text-xs text-muted-foreground">
          {formatDistanceToNow(new Date(campaign.created_at), {
            addSuffix: true,
            locale: bs,
          })}
        </div>
      </CardContent>
    </Card>
  );
}
