'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ShoppingCart, X, Check } from 'lucide-react';
import { toast } from 'sonner';
import { createPackageOrder } from '@/lib/offers';

interface PackageOrderModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  packageData: {
    id: number;
    platform_id: number;
    platform_name: string;
    platform_icon: string;
    content_type_id: number;
    content_type_name: string;
    price: number;
    currency: string;
    quantity?: number;
    video_duration?: string | null;
    auto_generated_name: string;
  };
  influencerId: string;
  influencerName: string;
}

export function PackageOrderModal({
  isOpen,
  onClose,
  onSuccess,
  packageData,
  influencerId,
  influencerName,
}: PackageOrderModalProps) {
  const [isLoading, setIsLoading] = useState(false);

  const handleConfirmOrder = async () => {
    try {
      setIsLoading(true);

      const orderData = {
        influencer_id: influencerId,
        package_id: packageData.id,
        package_name: packageData.auto_generated_name,
        price: packageData.price,
        platform_name: packageData.platform_name,
        content_type_name: packageData.content_type_name,
      };

      const { error } = await createPackageOrder(orderData);

      if (error) {
        toast.error('Greška pri kreiranju narudžbe');
        return;
      }

      toast.success('Narudžba je uspješno poslana!');
      onSuccess();
      onClose();
    } catch (error) {
      console.error('Error creating package order:', error);
      toast.error('Neočekivana greška');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <ShoppingCart className="h-5 w-5" />
            Potvrda narudžbe
          </DialogTitle>
          <DialogDescription>
            Molimo potvrdite detalje vaše narudžbe
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Influencer info */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm">Influencer</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="font-medium">{influencerName}</p>
            </CardContent>
          </Card>

          {/* Package details */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm">Detalji paketa</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex items-center gap-2">
                <span className="text-lg">{packageData.platform_icon}</span>
                <Badge variant="secondary">{packageData.platform_name}</Badge>
              </div>
              
              <div>
                <p className="font-medium">{packageData.auto_generated_name}</p>
                <p className="text-sm text-muted-foreground">
                  {packageData.content_type_name}
                </p>
              </div>

              {packageData.quantity && packageData.quantity > 1 && (
                <div className="text-sm">
                  <span className="text-muted-foreground">Količina: </span>
                  <span className="font-medium">{packageData.quantity}</span>
                </div>
              )}

              {packageData.video_duration && (
                <div className="text-sm">
                  <span className="text-muted-foreground">Trajanje: </span>
                  <span className="font-medium">{packageData.video_duration}</span>
                </div>
              )}

              <hr className="my-2" />

              <div className="flex justify-between items-center">
                <span className="font-medium">Ukupno:</span>
                <span className="text-xl font-bold text-primary">
                  {packageData.price} {packageData.currency}
                </span>
              </div>
            </CardContent>
          </Card>

          {/* Info message */}
          <div className="bg-muted/50 p-3 rounded-lg">
            <p className="text-sm text-muted-foreground">
              Nakon potvrde, vaša narudžba će biti poslana influenceru na razmatranje. 
              Moći ćete pratiti status narudžbe u vašem dashboard-u.
            </p>
          </div>
        </div>

        {/* Action buttons */}
        <div className="flex justify-between pt-4">
          <Button variant="outline" onClick={onClose} disabled={isLoading}>
            <X className="mr-2 h-4 w-4" />
            Otkaži
          </Button>
          <Button onClick={handleConfirmOrder} disabled={isLoading}>
            {isLoading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Šalje se...
              </>
            ) : (
              <>
                <Check className="mr-2 h-4 w-4" />
                Potvrdi narudžbu
              </>
            )}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
