-- <PERSON><PERSON><PERSON><PERSON><PERSON> tabele za direktne ponude
CREATE TABLE direct_offers (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  business_id UUID NOT NULL REFERENCES businesses(id) ON DELETE CASCADE,
  influencer_id UUID NOT NULL REFERENCES influencers(id) ON DELETE CASCADE,
  title VARCHAR(200) NOT NULL,
  description TEXT NOT NULL,
  budget DECIMAL(10,2) NOT NULL,
  content_types TEXT[] NOT NULL,
  platforms TEXT[] NOT NULL,
  deadline DATE,
  requirements TEXT,
  deliverables TEXT,
  status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'accepted', 'rejected', 'completed', 'cancelled')),
  business_message TEXT,
  influencer_response TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  accepted_at TIMESTAMP WITH TIME ZONE,
  rejected_at TIMESTAMP WITH TIME ZONE
);

-- <PERSON><PERSON><PERSON> za performanse
CREATE INDEX idx_direct_offers_business_id ON direct_offers(business_id);
CREATE INDEX idx_direct_offers_influencer_id ON direct_offers(influencer_id);
CREATE INDEX idx_direct_offers_status ON direct_offers(status);
CREATE INDEX idx_direct_offers_created_at ON direct_offers(created_at);

-- RLS politike
ALTER TABLE direct_offers ENABLE ROW LEVEL SECURITY;

-- Biznis može vidjeti svoje ponude
CREATE POLICY "Businesses can view their own offers" ON direct_offers
  FOR SELECT USING (
    business_id IN (
      SELECT id FROM businesses WHERE user_id = auth.uid()
    )
  );

-- Influencer može vidjeti ponude poslane njemu
CREATE POLICY "Influencers can view offers sent to them" ON direct_offers
  FOR SELECT USING (
    influencer_id IN (
      SELECT id FROM influencers WHERE user_id = auth.uid()
    )
  );

-- Biznis može kreirati ponude
CREATE POLICY "Businesses can create offers" ON direct_offers
  FOR INSERT WITH CHECK (
    business_id IN (
      SELECT id FROM businesses WHERE user_id = auth.uid()
    )
  );

-- Biznis može ažurirati svoje ponude
CREATE POLICY "Businesses can update their own offers" ON direct_offers
  FOR UPDATE USING (
    business_id IN (
      SELECT id FROM businesses WHERE user_id = auth.uid()
    )
  );

-- Influencer može ažurirati status ponuda poslanih njemu
CREATE POLICY "Influencers can update offer status" ON direct_offers
  FOR UPDATE USING (
    influencer_id IN (
      SELECT id FROM influencers WHERE user_id = auth.uid()
    )
  ) WITH CHECK (
    influencer_id IN (
      SELECT id FROM influencers WHERE user_id = auth.uid()
    )
  );

-- Trigger za ažuriranje updated_at
CREATE OR REPLACE FUNCTION update_direct_offers_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_direct_offers_updated_at
  BEFORE UPDATE ON direct_offers
  FOR EACH ROW
  EXECUTE FUNCTION update_direct_offers_updated_at();
