'use client';

import { useState } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { AlertTriangle } from 'lucide-react';
import { toast } from 'sonner';
import { rejectJobCompletion } from '@/lib/job-completions';

interface RejectJobModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  jobCompletionId: string;
  influencerName: string;
}

export function RejectJobModal({
  isOpen,
  onClose,
  onSuccess,
  jobCompletionId,
  influencerName,
}: RejectJobModalProps) {
  const [businessNotes, setBusinessNotes] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async () => {
    if (!businessNotes.trim()) {
      toast.error('Molimo objasnite razlog odbacivanja');
      return;
    }

    setIsSubmitting(true);
    try {
      const { error } = await rejectJobCompletion(
        jobCompletionId,
        businessNotes.trim()
      );

      if (error) {
        toast.error('Greška pri odbacivanju rada');
        return;
      }

      toast.success('Rad je odbačen i influencer je obaviješten');
      onSuccess();
      onClose();

      // Reset form
      setBusinessNotes('');
    } catch (error) {
      toast.error('Dogodila se neočekivana greška');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    if (!isSubmitting) {
      onClose();
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-destructive" />
            Odbaci rad
          </DialogTitle>
          <DialogDescription>
            Odbacujete rad od <strong>{influencerName}</strong>. Molimo
            objasnite razlog odbacivanja.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4 py-4">
          <div className="space-y-2">
            <Label htmlFor="businessNotes">Razlog odbacivanja *</Label>
            <Textarea
              id="businessNotes"
              placeholder="Objasnite zašto odbacujete ovaj rad..."
              value={businessNotes}
              onChange={e => setBusinessNotes(e.target.value)}
              rows={4}
              required
            />
            <p className="text-sm text-muted-foreground">
              Ovaj komentar će biti poslan influenceru.
            </p>
          </div>
        </div>

        <DialogFooter>
          <Button
            variant="outline"
            onClick={handleClose}
            disabled={isSubmitting}
          >
            Otkaži
          </Button>
          <Button
            variant="destructive"
            onClick={handleSubmit}
            disabled={isSubmitting || !businessNotes.trim()}
          >
            {isSubmitting ? 'Odbacuje se...' : 'Odbaci rad'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
