-- PRICING PACKAGES SYSTEM UPDATE
-- Dodavanje polja za napredni pricing sistem

-- 1. Dodavanje novih kolona u influencer_platform_pricing tabelu
ALTER TABLE influencer_platform_pricing 
ADD COLUMN IF NOT EXISTS quantity INTEGER DEFAULT 1,
ADD COLUMN IF NOT EXISTS video_duration VARCHAR(20),
ADD COLUMN IF NOT EXISTS auto_generated_name VARCHAR(200);

-- 2. Dodavanje komentara za nova polja
COMMENT ON COLUMN influencer_platform_pricing.quantity IS 'Količina sadržaja u paketu (npr. 2 za "2x Instagram Story")';
COMMENT ON COLUMN influencer_platform_pricing.video_duration IS 'Trajanje videa: 30s, 1min, 3min, 5min, 10min, 15min, 30min';
COMMENT ON COLUMN influencer_platform_pricing.auto_generated_name IS 'Automatski generisan naziv paketa (npr. "2x Instagram Story", "1x TikTok Video 30s")';

-- 3. K<PERSON>iranje indeksa za nova polja
CREATE INDEX IF NOT EXISTS idx_pricing_quantity ON influencer_platform_pricing(quantity);
CREATE INDEX IF NOT EXISTS idx_pricing_video_duration ON influencer_platform_pricing(video_duration);
CREATE INDEX IF NOT EXISTS idx_pricing_package_name ON influencer_platform_pricing(auto_generated_name);

-- 4. Funkcija za automatsko generiranje naziva paketa
CREATE OR REPLACE FUNCTION generate_package_name(
  p_quantity INTEGER,
  p_platform_name VARCHAR,
  p_content_type_name VARCHAR,
  p_video_duration VARCHAR DEFAULT NULL
) RETURNS VARCHAR AS $$
DECLARE
  package_name VARCHAR(200);
BEGIN
  -- Osnovni naziv: "2x Instagram Story"
  package_name := p_quantity || 'x ' || p_platform_name || ' ' || p_content_type_name;
  
  -- Dodaj trajanje videa ako postoji i ako je tip sadržaja video
  IF p_video_duration IS NOT NULL AND LOWER(p_content_type_name) LIKE '%video%' THEN
    package_name := package_name || ' ' || p_video_duration;
  END IF;
  
  RETURN package_name;
END;
$$ LANGUAGE plpgsql;

-- 5. Trigger funkcija za automatsko ažuriranje naziva paketa
CREATE OR REPLACE FUNCTION update_package_name()
RETURNS TRIGGER AS $$
DECLARE
  platform_name VARCHAR(50);
  content_type_name VARCHAR(100);
BEGIN
  -- Dobij naziv platforme
  SELECT name INTO platform_name 
  FROM platforms 
  WHERE id = NEW.platform_id;
  
  -- Dobij naziv tipa sadržaja
  SELECT name INTO content_type_name 
  FROM content_types 
  WHERE id = NEW.content_type_id;
  
  -- Generiši naziv paketa
  NEW.auto_generated_name := generate_package_name(
    COALESCE(NEW.quantity, 1),
    platform_name,
    content_type_name,
    NEW.video_duration
  );
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 6. Kreiranje trigger-a za automatsko ažuriranje naziva
DROP TRIGGER IF EXISTS trigger_update_package_name ON influencer_platform_pricing;
CREATE TRIGGER trigger_update_package_name
  BEFORE INSERT OR UPDATE ON influencer_platform_pricing
  FOR EACH ROW
  EXECUTE FUNCTION update_package_name();

-- 7. Ažuriranje postojećih zapisa (ako postoje)
UPDATE influencer_platform_pricing 
SET 
  quantity = COALESCE(quantity, 1),
  auto_generated_name = generate_package_name(
    COALESCE(quantity, 1),
    (SELECT name FROM platforms WHERE id = platform_id),
    (SELECT name FROM content_types WHERE id = content_type_id),
    video_duration
  )
WHERE auto_generated_name IS NULL;

-- 8. Dodavanje constraint-a za validaciju
ALTER TABLE influencer_platform_pricing 
ADD CONSTRAINT check_quantity_positive CHECK (quantity > 0);

ALTER TABLE influencer_platform_pricing 
ADD CONSTRAINT check_valid_video_duration 
CHECK (video_duration IS NULL OR video_duration IN ('30s', '1min', '3min', '5min', '10min', '15min', '30min'));

-- 9. Funkcija za dobijanje paketa sa detaljima
CREATE OR REPLACE FUNCTION get_influencer_packages_with_details(influencer_uuid UUID)
RETURNS TABLE (
  id INTEGER,
  influencer_id UUID,
  platform_id INTEGER,
  content_type_id INTEGER,
  quantity INTEGER,
  video_duration VARCHAR,
  price DECIMAL,
  currency VARCHAR,
  auto_generated_name VARCHAR,
  is_available BOOLEAN,
  created_at TIMESTAMP WITH TIME ZONE,
  updated_at TIMESTAMP WITH TIME ZONE,
  platform_name VARCHAR,
  platform_icon VARCHAR,
  platform_slug VARCHAR,
  content_type_name VARCHAR,
  content_type_slug VARCHAR,
  content_type_description TEXT
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    ipp.id,
    ipp.influencer_id,
    ipp.platform_id,
    ipp.content_type_id,
    ipp.quantity,
    ipp.video_duration,
    ipp.price,
    ipp.currency,
    ipp.auto_generated_name,
    ipp.is_available,
    ipp.created_at,
    ipp.updated_at,
    p.name as platform_name,
    p.icon as platform_icon,
    p.slug as platform_slug,
    ct.name as content_type_name,
    ct.slug as content_type_slug,
    ct.description as content_type_description
  FROM influencer_platform_pricing ipp
  JOIN platforms p ON ipp.platform_id = p.id
  JOIN content_types ct ON ipp.content_type_id = ct.id
  WHERE 
    ipp.influencer_id = influencer_uuid
    AND ipp.is_available = true
    AND p.is_active = true
    AND ct.is_active = true
  ORDER BY ipp.created_at DESC;
END;
$$ LANGUAGE plpgsql;

-- 10. Funkcija za marketplace prikaz paketa
CREATE OR REPLACE FUNCTION get_marketplace_pricing_packages(influencer_uuid UUID)
RETURNS TABLE (
  platform_id INTEGER,
  platform_name VARCHAR,
  platform_icon VARCHAR,
  content_type_id INTEGER,
  content_type_name VARCHAR,
  package_name VARCHAR,
  quantity INTEGER,
  video_duration VARCHAR,
  price DECIMAL,
  currency VARCHAR
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    ipp.platform_id,
    p.name as platform_name,
    p.icon as platform_icon,
    ipp.content_type_id,
    ct.name as content_type_name,
    ipp.auto_generated_name as package_name,
    ipp.quantity,
    ipp.video_duration,
    ipp.price,
    ipp.currency
  FROM influencer_platform_pricing ipp
  JOIN platforms p ON ipp.platform_id = p.id
  JOIN content_types ct ON ipp.content_type_id = ct.id
  WHERE 
    ipp.influencer_id = influencer_uuid
    AND ipp.is_available = true
    AND p.is_active = true
    AND ct.is_active = true
  ORDER BY ipp.price ASC;
END;
$$ LANGUAGE plpgsql;

-- 11. Dodavanje RLS policy za nove funkcije
CREATE POLICY "Influencers can view own packages" ON influencer_platform_pricing 
  FOR SELECT USING (auth.uid() = influencer_id);

CREATE POLICY "Influencers can insert own packages" ON influencer_platform_pricing 
  FOR INSERT WITH CHECK (auth.uid() = influencer_id);

CREATE POLICY "Influencers can update own packages" ON influencer_platform_pricing 
  FOR UPDATE USING (auth.uid() = influencer_id);

CREATE POLICY "Influencers can delete own packages" ON influencer_platform_pricing 
  FOR DELETE USING (auth.uid() = influencer_id);

-- 12. Kreiranje view-a za lakše dohvatanje paketa
CREATE OR REPLACE VIEW influencer_packages_view AS
SELECT 
  ipp.*,
  p.name as platform_name,
  p.icon as platform_icon,
  p.slug as platform_slug,
  ct.name as content_type_name,
  ct.slug as content_type_slug,
  ct.description as content_type_description
FROM influencer_platform_pricing ipp
JOIN platforms p ON ipp.platform_id = p.id
JOIN content_types ct ON ipp.content_type_id = ct.id
WHERE 
  ipp.is_available = true
  AND p.is_active = true
  AND ct.is_active = true;

-- Dodavanje RLS policy za view
ALTER VIEW influencer_packages_view OWNER TO postgres;

-- 13. Kreiranje helper funkcije za validaciju paketa
CREATE OR REPLACE FUNCTION validate_package_data(
  p_platform_id INTEGER,
  p_content_type_id INTEGER,
  p_quantity INTEGER,
  p_video_duration VARCHAR DEFAULT NULL
) RETURNS BOOLEAN AS $$
DECLARE
  platform_exists BOOLEAN;
  content_type_exists BOOLEAN;
  content_type_name VARCHAR;
BEGIN
  -- Provjeri da li platforma postoji i aktivna je
  SELECT EXISTS(
    SELECT 1 FROM platforms 
    WHERE id = p_platform_id AND is_active = true
  ) INTO platform_exists;
  
  -- Provjeri da li tip sadržaja postoji, aktivan je i pripada platformi
  SELECT EXISTS(
    SELECT 1 FROM content_types 
    WHERE id = p_content_type_id 
      AND platform_id = p_platform_id 
      AND is_active = true
  ) INTO content_type_exists;
  
  -- Ako je video duration specificiran, provjeri da li je tip sadržaja video
  IF p_video_duration IS NOT NULL THEN
    SELECT name INTO content_type_name
    FROM content_types 
    WHERE id = p_content_type_id;
    
    IF content_type_name IS NULL OR LOWER(content_type_name) NOT LIKE '%video%' THEN
      RETURN FALSE;
    END IF;
  END IF;
  
  RETURN platform_exists AND content_type_exists AND p_quantity > 0;
END;
$$ LANGUAGE plpgsql;
