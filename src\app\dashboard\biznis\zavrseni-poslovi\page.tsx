'use client';

import { useEffect, useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import {
  Loader2,
  RefreshCw,
  Clock,
  CheckCircle,
  XCircle,
  FileText,
} from 'lucide-react';
import { JobCompletionCard } from '@/components/job-completion/JobCompletionCard';
import {
  getUserJobCompletions,
  JobCompletionWithDetails,
} from '@/lib/job-completions';
import { getCurrentUser } from '@/lib/profiles';
import { toast } from 'sonner';
import { DashboardLayout } from '@/components/dashboard/DashboardLayout';

export default function BiznisZavrseniPosloviPage() {
  const [jobCompletions, setJobCompletions] = useState<
    JobCompletionWithDetails[]
  >([]);
  const [isLoading, setIsLoading] = useState(true);
  const [userType, setUserType] = useState<'influencer' | 'business'>(
    'business'
  );
  const [activeTab, setActiveTab] = useState('all');

  const loadJobCompletions = async () => {
    try {
      setIsLoading(true);
      const { data, error } = await getUserJobCompletions();

      if (error) {
        toast.error('Neuspješno učitavanje završenih poslova');
        return;
      }

      setJobCompletions(data || []);
    } catch (error) {
      toast.error('Dogodila se neočekivana greška');
    } finally {
      setIsLoading(false);
    }
  };

  const loadUserProfile = async () => {
    try {
      const { data: user, error } = await getCurrentUser();
      if (error || !user) {
        toast.error('Neuspješno učitavanje korisničkog profila');
        return;
      }
      setUserType(user.user_type);
    } catch (error) {
      toast.error('Neuspješno učitavanje korisničkog profila');
    }
  };

  useEffect(() => {
    loadUserProfile();
    loadJobCompletions();
  }, []);

  const handleRefresh = () => {
    loadJobCompletions();
  };

  const handleJobCompletionUpdate = () => {
    loadJobCompletions();
  };

  // Filter job completions by status
  const filterByStatus = (status?: string) => {
    if (!status || status === 'all') return jobCompletions;
    return jobCompletions.filter(jc => jc.status === status);
  };

  const getStatusCounts = () => {
    return {
      all: jobCompletions.length,
      pending: jobCompletions.filter(jc => jc.status === 'pending').length,
      submitted: jobCompletions.filter(jc => jc.status === 'submitted').length,
      approved: jobCompletions.filter(jc => jc.status === 'approved').length,
      rejected: jobCompletions.filter(jc => jc.status === 'rejected').length,
      completed: jobCompletions.filter(jc => jc.status === 'completed').length,
    };
  };

  const statusCounts = getStatusCounts();
  const filteredJobCompletions = filterByStatus(activeTab);

  if (isLoading) {
    return (
      <DashboardLayout requiredUserType="business">
        <div className="flex items-center justify-center h-64">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout requiredUserType="business">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-3xl font-bold">Završeni poslovi</h1>
          <p className="text-muted-foreground mt-1">
            Pregled i upravljanje završenim poslovima
          </p>
        </div>
        <Button onClick={handleRefresh} variant="outline" size="sm">
          <RefreshCw className="h-4 w-4 mr-2" />
          Osvježi
        </Button>
      </div>

      <Tabs
        value={activeTab}
        onValueChange={setActiveTab}
        className="space-y-6"
      >
        <TabsList className="grid w-full grid-cols-6">
          <TabsTrigger value="all" className="flex items-center gap-2">
            <FileText className="h-4 w-4" />
            Svi ({statusCounts.all})
          </TabsTrigger>
          <TabsTrigger value="pending" className="flex items-center gap-2">
            <Clock className="h-4 w-4" />
            Na čekanju ({statusCounts.pending})
          </TabsTrigger>
          <TabsTrigger value="submitted" className="flex items-center gap-2">
            <FileText className="h-4 w-4" />
            Predati ({statusCounts.submitted})
          </TabsTrigger>
          <TabsTrigger value="approved" className="flex items-center gap-2">
            <CheckCircle className="h-4 w-4" />
            Odobreni ({statusCounts.approved})
          </TabsTrigger>
          <TabsTrigger value="rejected" className="flex items-center gap-2">
            <XCircle className="h-4 w-4" />
            Odbačeni ({statusCounts.rejected})
          </TabsTrigger>
          <TabsTrigger value="completed" className="flex items-center gap-2">
            <CheckCircle className="h-4 w-4" />
            Završeni ({statusCounts.completed})
          </TabsTrigger>
        </TabsList>

        {[
          'all',
          'pending',
          'submitted',
          'approved',
          'rejected',
          'completed',
        ].map(status => (
          <TabsContent key={status} value={status} className="space-y-4">
            {filterByStatus(status).length === 0 ? (
              <Card>
                <CardContent className="p-8 text-center">
                  <div className="flex flex-col items-center gap-2">
                    <FileText className="h-12 w-12 text-muted-foreground/50" />
                    <h3 className="text-lg font-medium">Nema poslova</h3>
                    <p className="text-muted-foreground">
                      {status === 'all' && 'Nema završenih poslova'}
                      {status === 'pending' && 'Nema poslova na čekanju'}
                      {status === 'submitted' && 'Nema predatih poslova'}
                      {status === 'approved' && 'Nema odobrenih poslova'}
                      {status === 'rejected' && 'Nema odbačenih poslova'}
                      {status === 'completed' &&
                        'Nema potpuno završenih poslova'}
                    </p>
                  </div>
                </CardContent>
              </Card>
            ) : (
              <div className="space-y-4">
                {filterByStatus(status).map(jobCompletion => (
                  <JobCompletionCard
                    key={jobCompletion.id}
                    jobCompletion={jobCompletion}
                    userType={userType}
                    onUpdate={handleJobCompletionUpdate}
                  />
                ))}
              </div>
            )}
          </TabsContent>
        ))}
      </Tabs>
    </DashboardLayout>
  );
}
