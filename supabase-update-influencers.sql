-- <PERSON>žuriranje influencers tabele sa novim poljima
-- Pokrenuti u Supabase SQL Editor

-- Dodavanje novih kolona u influencers tabelu
ALTER TABLE influencers 
ADD COLUMN IF NOT EXISTS gender VARCHAR(20),
ADD COLUMN IF NOT EXISTS age INTEGER;

-- <PERSON><PERSON><PERSON><PERSON> check constraint za age
ALTER TABLE influencers 
ADD CONSTRAINT check_age_range CHECK (age IS NULL OR (age >= 13 AND age <= 100));

-- <PERSON>davanje check constraint za gender
ALTER TABLE influencers 
ADD CONSTRAINT check_gender_values CHECK (
  gender IS NULL OR gender IN ('male', 'female', 'other', 'prefer_not_to_say')
);

-- Uklanjanje niche kolone jer sada koristimo categories
ALTER TABLE influencers DROP COLUMN IF EXISTS niche;

-- <PERSON><PERSON>iranje indeksa za performanse
CREATE INDEX IF NOT EXISTS idx_influencers_gender ON influencers(gender);
CREATE INDEX IF NOT EXISTS idx_influencers_age ON influencers(age);

-- <PERSON><PERSON><PERSON><PERSON><PERSON> TypeScript tipova (informativno - treba ažurirati u kodu)
/*
<PERSON><PERSON><PERSON>rati u src/lib/database.types.ts:

influencers tabela:
- dodati: gender: string | null
- dodati: age: number | null  
- ukloniti: niche: string | null
*/
