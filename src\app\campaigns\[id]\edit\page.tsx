'use client';

import { useEffect, useState } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { getCampaignForEdit, updateCampaign } from '@/lib/campaigns';
import { CreateCampaignForm } from '@/components/campaigns/create-campaign-form';
import { Button } from '@/components/ui/button';
import { ArrowLeft, Loader2 } from 'lucide-react';
import Link from 'next/link';

interface Campaign {
  id: string;
  title: string;
  description: string;
  budget: number | null;
  content_types: string[];
  min_followers?: number;
  max_followers?: number;
  age_range_min?: number;
  age_range_max?: number;
  gender?: string;
  campaign_platforms: Array<{
    platform_id: string;
    platforms: { name: string };
  }>;
  campaign_categories: Array<{
    category_id: string;
    categories: { name: string };
  }>;
}

export default function EditCampaignPage() {
  const params = useParams();
  const router = useRouter();
  const campaignId = params.id as string;

  const [campaign, setCampaign] = useState<Campaign | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [updating, setUpdating] = useState(false);

  useEffect(() => {
    loadCampaign();
  }, [campaignId]);

  const loadCampaign = async () => {
    try {
      setLoading(true);
      const { data, error } = await getCampaignForEdit(campaignId);

      if (error) {
        setError(error.message || 'Failed to load campaign');
        return;
      }

      if (!data) {
        setError('Campaign not found or not editable');
        return;
      }

      setCampaign(data);
    } catch (err) {
      setError('Failed to load campaign');
      console.error('Error loading campaign:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (formData: any) => {
    try {
      setUpdating(true);

      const { data, error } = await updateCampaign(campaignId, formData);

      if (error) {
        throw new Error(error.message || 'Failed to update campaign');
      }

      // Redirect to campaign details or dashboard
      router.push(`/campaigns/${campaignId}`);
    } catch (err: any) {
      console.error('Error updating campaign:', err);
      setError(err.message || 'Failed to update campaign');
    } finally {
      setUpdating(false);
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="flex items-center gap-2">
            <Loader2 className="h-6 w-6 animate-spin" />
            <span>Loading campaign...</span>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-2xl mx-auto">
          <div className="mb-6">
            <Link href="/dashboard/campaigns">
              <Button variant="ghost" className="mb-4">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Campaigns
              </Button>
            </Link>
          </div>

          <div className="bg-red-50 border border-red-200 rounded-lg p-6 text-center">
            <h2 className="text-xl font-semibold text-red-800 mb-2">Error</h2>
            <p className="text-red-600">{error}</p>
            <Button
              onClick={() => router.push('/dashboard/campaigns')}
              className="mt-4"
            >
              Go to Campaigns
            </Button>
          </div>
        </div>
      </div>
    );
  }

  if (!campaign) {
    return null;
  }

  // Transform campaign data for the form
  const initialData = {
    title: campaign.title,
    description: campaign.description,
    budget: campaign.budget,
    content_types: campaign.content_types,
    min_followers: campaign.min_followers,
    max_followers: campaign.max_followers,
    age_range_min: campaign.age_range_min,
    age_range_max: campaign.age_range_max,
    gender: campaign.gender,
    platforms: campaign.campaign_platforms?.map(cp => cp.platform_id) || [],
    categories: campaign.campaign_categories?.map(cc => cc.category_id) || [],
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-4xl mx-auto">
        <div className="mb-6">
          <Link href="/dashboard/campaigns">
            <Button variant="ghost" className="mb-4">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Campaigns
            </Button>
          </Link>

          <h1 className="text-3xl font-bold">Edit Campaign</h1>
          <p className="text-muted-foreground mt-2">
            Update your campaign details. You can only edit campaigns in draft
            status.
          </p>
        </div>

        <CreateCampaignForm
          initialData={initialData}
          onSubmit={handleSubmit}
          isEditing={true}
          isSubmitting={updating}
          submitButtonText={updating ? 'Updating...' : 'Update Campaign'}
        />
      </div>
    </div>
  );
}
