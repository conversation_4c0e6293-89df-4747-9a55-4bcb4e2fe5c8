'use client';

import { useState } from 'react';
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Star } from 'lucide-react';
import { toast } from 'sonner';
import { approveJobCompletion } from '@/lib/job-completions';

interface ApproveJobModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  jobCompletionId: string;
  influencerName: string;
}

export function ApproveJobModal({
  isOpen,
  onClose,
  onSuccess,
  jobCompletionId,
  influencerName,
}: ApproveJobModalProps) {
  const [rating, setRating] = useState(0);
  const [hoveredRating, setHoveredRating] = useState(0);
  const [reviewNotes, setReviewNotes] = useState('');
  const [businessNotes, setBusinessNotes] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async () => {
    if (rating === 0) {
      toast.error('Molimo odaberite ocjenu');
      return;
    }

    setIsSubmitting(true);
    try {
      const result = await approveJobCompletion(
        jobCompletionId,
        rating,
        reviewNotes.trim() || undefined,
        businessNotes.trim() || undefined
      );

      if (result.error) {
        console.error('Error approving job completion:', result.error);
        toast.error('Greška pri odobravanju rada');
        return;
      }

      toast.success('Rad je uspješno odobren i ocjena je poslana');
      onSuccess();
      onClose();

      // Reset form
      setRating(0);
      setReviewNotes('');
      setBusinessNotes('');
    } catch (error) {
      console.error('Unexpected error:', error);
      toast.error('Dogodila se neočekivana greška');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    if (!isSubmitting) {
      onClose();
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Odobri rad i ocijeni influencera</DialogTitle>
          <DialogDescription>
            Odobravate rad od <strong>{influencerName}</strong>. Molimo ostavite
            ocjenu i komentar.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6 py-4">
          {/* Rating */}
          <div className="space-y-2">
            <Label>Ocjena (1-5 zvjezdica) *</Label>
            <div className="flex items-center gap-1">
              {[1, 2, 3, 4, 5].map(star => (
                <button
                  key={star}
                  type="button"
                  className="p-1 hover:scale-110 transition-transform"
                  onMouseEnter={() => setHoveredRating(star)}
                  onMouseLeave={() => setHoveredRating(0)}
                  onClick={() => setRating(star)}
                >
                  <Star
                    className={`h-8 w-8 ${
                      star <= (hoveredRating || rating)
                        ? 'fill-yellow-400 text-yellow-400'
                        : 'text-gray-300'
                    }`}
                  />
                </button>
              ))}
              {rating > 0 && (
                <span className="ml-2 text-sm text-muted-foreground">
                  {rating} od 5 zvjezdica
                </span>
              )}
            </div>
          </div>

          {/* Review Notes */}
          <div className="space-y-2">
            <Label htmlFor="reviewNotes">
              Javni komentar (vidljiv influenceru)
            </Label>
            <Textarea
              id="reviewNotes"
              placeholder="Ostavite komentar o radu influencera..."
              value={reviewNotes}
              onChange={e => setReviewNotes(e.target.value)}
              rows={3}
            />
          </div>

          {/* Business Notes */}
          <div className="space-y-2">
            <Label htmlFor="businessNotes">
              Privatne napomene (samo za vas)
            </Label>
            <Textarea
              id="businessNotes"
              placeholder="Privatne napomene o ovom poslu..."
              value={businessNotes}
              onChange={e => setBusinessNotes(e.target.value)}
              rows={2}
            />
          </div>
        </div>

        <DialogFooter>
          <Button
            variant="outline"
            onClick={handleClose}
            disabled={isSubmitting}
          >
            Otkaži
          </Button>
          <Button
            onClick={handleSubmit}
            disabled={isSubmitting || rating === 0}
          >
            {isSubmitting ? 'Odobrava se...' : 'Odobri rad'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
