'use client';

import { <PERSON>, CardContent, CardHeader } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Star, User, Calendar } from 'lucide-react';
import { JobReviewWithDetails } from '@/lib/job-reviews';

interface ReviewCardProps {
  review: JobReviewWithDetails;
  showJobDetails?: boolean;
}

export function ReviewCard({ review, showJobDetails = true }: ReviewCardProps) {
  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const renderStars = (rating: number | null) => {
    if (!rating) return null;

    return (
      <div className="flex items-center gap-1">
        {[1, 2, 3, 4, 5].map(star => (
          <Star
            key={star}
            className={`h-4 w-4 ${
              star <= rating
                ? 'fill-yellow-400 text-yellow-400'
                : 'text-gray-300'
            }`}
          />
        ))}
        <span className="ml-2 text-sm font-medium">{rating}/5</span>
      </div>
    );
  };

  const getReviewTypeLabel = (reviewType: string | null) => {
    switch (reviewType) {
      case 'influencer_to_business':
        return 'Influencer → Business';
      case 'business_to_influencer':
        return 'Business → Influencer';
      default:
        return 'Review';
    }
  };

  const getReviewTypeColor = (reviewType: string | null) => {
    switch (reviewType) {
      case 'influencer_to_business':
        return 'bg-blue-100 text-blue-800';
      case 'business_to_influencer':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <Card className="w-full">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-center gap-3">
            <Avatar className="h-10 w-10">
              <AvatarImage src={review.reviewer_profile?.avatar_url || ''} />
              <AvatarFallback>
                <User className="h-5 w-5" />
              </AvatarFallback>
            </Avatar>
            <div>
              <p className="font-medium text-sm">
                {review.reviewer_profile?.full_name || 'Anonymous'}
              </p>
              <p className="text-xs text-muted-foreground">
                @{review.reviewer_profile?.username}
              </p>
              <div className="flex items-center gap-2 mt-1">
                <Badge className={getReviewTypeColor(review.review_type)}>
                  {getReviewTypeLabel(review.review_type)}
                </Badge>
                <div className="flex items-center gap-1 text-xs text-muted-foreground">
                  <Calendar className="h-3 w-3" />
                  {formatDate(review.created_at)}
                </div>
              </div>
            </div>
          </div>
          <div className="text-right">{renderStars(review.rating)}</div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Job Details */}
        {showJobDetails &&
          review.job_completion?.campaign_application?.campaign && (
            <div className="space-y-2">
              <h4 className="font-medium text-sm">Campaign</h4>
              <div className="bg-muted p-3 rounded-md">
                <p className="text-sm font-medium">
                  {review.job_completion.campaign_application.campaign.title}
                </p>
                <p className="text-xs text-muted-foreground">
                  by{' '}
                  {
                    review.job_completion.campaign_application.campaign.business
                      ?.company_name
                  }
                </p>
              </div>
            </div>
          )}

        {/* Review Comment */}
        {review.comment && (
          <div className="space-y-2">
            <h4 className="font-medium text-sm">Review</h4>
            <div className="bg-muted p-3 rounded-md">
              <p className="text-sm">{review.comment}</p>
            </div>
          </div>
        )}

        {/* Reviewee Info */}
        <div className="space-y-2">
          <h4 className="font-medium text-sm">Reviewed</h4>
          <div className="flex items-center gap-2">
            <Avatar className="h-8 w-8">
              <AvatarImage src={review.reviewee_profile?.avatar_url || ''} />
              <AvatarFallback>
                <User className="h-4 w-4" />
              </AvatarFallback>
            </Avatar>
            <div>
              <p className="text-sm font-medium">
                {review.reviewee_profile?.full_name || 'Unknown'}
              </p>
              <p className="text-xs text-muted-foreground">
                @{review.reviewee_profile?.username} •{' '}
                {review.reviewee_profile?.user_type}
              </p>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
