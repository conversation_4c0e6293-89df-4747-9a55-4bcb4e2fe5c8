'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Upload, X, Send, Loader2 } from 'lucide-react';
import { submitJobCompletion } from '@/lib/job-completions';
import { toast } from 'sonner';

interface CampaignJobSubmissionFormProps {
  campaignApplicationId: string;
  campaignTitle?: string;
  businessName?: string;
  businessAvatar?: string | null;
  proposedRate?: number;
  onSuccess?: () => void;
  onCancel?: () => void;
}

export function CampaignJobSubmissionForm({
  campaignApplicationId,
  campaignTitle,
  businessName,
  businessAvatar,
  proposedRate,
  onSuccess,
  onCancel,
}: CampaignJobSubmissionFormProps) {
  const [submissionNotes, setSubmissionNotes] = useState('');
  const [files, setFiles] = useState<File[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      const newFiles = Array.from(e.target.files);
      setFiles(prev => [...prev, ...newFiles]);
    }
  };

  const removeFile = (index: number) => {
    setFiles(prev => prev.filter((_, i) => i !== index));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!submissionNotes.trim()) {
      toast.error('Molimo unesite napomene o završetku posla');
      return;
    }

    setIsSubmitting(true);

    try {
      // Convert files to base64 or upload them (simplified for now)
      const fileData =
        files.length > 0
          ? files.map(file => ({
              name: file.name,
              size: file.size,
              type: file.type,
            }))
          : undefined;

      const { error } = await submitJobCompletion(
        campaignApplicationId,
        submissionNotes,
        fileData
      );

      if (error) {
        toast.error('Greška pri slanju završetka posla');
        return;
      }

      toast.success('Završetak posla je uspešno poslan!');
      onSuccess?.();
    } catch (error) {
      console.error('Error submitting job completion:', error);
      toast.error('Greška pri slanju završetka posla');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-3">
          <Send className="h-5 w-5" />
          Pošaljite završetak posla
        </CardTitle>
        <CardDescription>
          Označite posao kao završen i pošaljite detalje biznisu na pregled
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Campaign Info */}
          <div className="flex items-center gap-4 p-4 bg-muted rounded-lg">
            <Avatar className="h-12 w-12">
              <AvatarImage src={businessAvatar || ''} />
              <AvatarFallback>{businessName?.charAt(0) || 'B'}</AvatarFallback>
            </Avatar>
            <div className="flex-1">
              <h3 className="font-medium">{campaignTitle || 'Kampanja'}</h3>
              <p className="text-sm text-muted-foreground">{businessName}</p>
              {proposedRate && (
                <Badge variant="secondary" className="mt-1">
                  {proposedRate} KM
                </Badge>
              )}
            </div>
          </div>

          {/* Submission Notes */}
          <div className="space-y-2">
            <Label htmlFor="submission-notes">
              Napomene o završetku posla *
            </Label>
            <Textarea
              id="submission-notes"
              placeholder="Opišite šta ste uradili, kako je prošla kampanja, rezultate koje ste postigli..."
              value={submissionNotes}
              onChange={e => setSubmissionNotes(e.target.value)}
              rows={4}
              required
            />
            <p className="text-xs text-muted-foreground">
              Detaljno opišite završene aktivnosti i rezultate
            </p>
          </div>

          {/* File Upload */}
          <div className="space-y-2">
            <Label htmlFor="files">Prilozi (opciono)</Label>
            <div className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-6 text-center">
              <Upload className="h-8 w-8 mx-auto mb-2 text-muted-foreground" />
              <p className="text-sm text-muted-foreground mb-2">
                Dodajte slike, videe ili druge fajlove koji pokazuju završen
                posao
              </p>
              <Input
                id="files"
                type="file"
                multiple
                accept="image/*,video/*,.pdf,.doc,.docx"
                onChange={handleFileChange}
                className="hidden"
              />
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => document.getElementById('files')?.click()}
              >
                Izaberite fajlove
              </Button>
            </div>

            {/* File List */}
            {files.length > 0 && (
              <div className="space-y-2">
                {files.map((file, index) => (
                  <div
                    key={index}
                    className="flex items-center justify-between p-2 bg-muted rounded"
                  >
                    <span className="text-sm truncate">{file.name}</span>
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => removeFile(index)}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Actions */}
          <div className="flex gap-3">
            <Button
              type="submit"
              disabled={isSubmitting || !submissionNotes.trim()}
              className="flex-1"
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Šalje se...
                </>
              ) : (
                <>
                  <Send className="mr-2 h-4 w-4" />
                  Pošalji završetak
                </>
              )}
            </Button>
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              disabled={isSubmitting}
            >
              Otkaži
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
}
