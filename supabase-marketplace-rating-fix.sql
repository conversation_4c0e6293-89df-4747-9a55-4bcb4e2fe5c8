-- Fix marketplace to include rating data
-- This updates the influencer_search_view to include average_rating and total_reviews

-- 1. Drop existing view
DROP VIEW IF EXISTS influencer_search_view;

-- 2. Recreate view with rating fields
CREATE VIEW influencer_search_view AS
SELECT 
  i.id,
  p.username,
  p.full_name,
  p.avatar_url,
  p.bio,
  p.location,
  i.gender,
  i.age,
  i.is_verified,
  p.average_rating,
  p.total_reviews,
  
  -- Kategorije kao array
  COALESCE(
    ARRAY_AGG(DISTINCT c.name) FILTER (WHERE c.name IS NOT NULL),
    '{}'::varchar[]
  ) as categories,
  
  -- <PERSON><PERSON> kao JSON
  COALESCE(
    JSON_AGG(
      JSON_BUILD_OBJECT(
        'platform_id', plat.id,
        'platform_name', plat.name,
        'platform_icon', plat.icon,
        'handle', ip.handle,
        'followers_count', ip.followers_count,
        'is_verified', ip.is_verified
      ) ORDER BY plat.name
    ) FILTER (WHERE plat.id IS NOT NULL AND ip.is_active = true),
    '[]'::json
  ) as platforms,

  -- <PERSON><PERSON><PERSON>ne kao <PERSON>N
  COALESCE(
    JSON_AGG(
      JSON_BUILD_OBJECT(
        'platform_id', pricing_plat.id,
        'platform_name', pricing_plat.name,
        'content_type_id', ct.id,
        'content_type_name', ct.name,
        'price', ipp.price,
        'currency', ipp.currency
      ) ORDER BY pricing_plat.name, ct.name
    ) FILTER (WHERE ipp.id IS NOT NULL AND ipp.is_available = true),
    '[]'::json
  ) as pricing,
  
  -- Minimum i maximum cijena za sortiranje
  COALESCE(MIN(ipp.price) FILTER (WHERE ipp.is_available = true), 0) as min_price,
  COALESCE(MAX(ipp.price) FILTER (WHERE ipp.is_available = true), 0) as max_price,
  
  -- Ukupan broj pratilaca
  COALESCE(SUM(ip.followers_count), 0) as total_followers,
  
  -- Full-text search vector
  to_tsvector('simple', 
    COALESCE(p.full_name, '') || ' ' ||
    COALESCE(p.username, '') || ' ' ||
    COALESCE(p.bio, '') || ' ' ||
    COALESCE(p.location, '') || ' ' ||
    COALESCE(STRING_AGG(DISTINCT c.name, ' '), '')
  ) as search_vector,
  
  p.created_at,
  p.updated_at

FROM influencers i
JOIN profiles p ON i.id = p.id
LEFT JOIN influencer_categories ic ON i.id = ic.influencer_id
LEFT JOIN categories c ON ic.category_id = c.id
LEFT JOIN influencer_platforms ip ON i.id = ip.influencer_id AND ip.is_active = true
LEFT JOIN platforms plat ON ip.platform_id = plat.id
LEFT JOIN influencer_platform_pricing ipp ON i.id = ipp.influencer_id AND ipp.is_available = true
LEFT JOIN platforms pricing_plat ON ipp.platform_id = pricing_plat.id
LEFT JOIN content_types ct ON ipp.content_type_id = ct.id

WHERE p.user_type = 'influencer'
GROUP BY i.id, p.username, p.full_name, p.avatar_url, p.bio, p.location, 
         i.gender, i.age, i.is_verified, p.average_rating, p.total_reviews, p.created_at, p.updated_at;

-- 3. Update the get_influencers_with_details function to include rating fields
CREATE OR REPLACE FUNCTION get_influencers_with_details(
  search_term TEXT DEFAULT '',
  min_followers INTEGER DEFAULT 0,
  max_followers INTEGER DEFAULT 999999999,
  min_price DECIMAL DEFAULT 0,
  max_price DECIMAL DEFAULT 999999,
  platform_filter TEXT DEFAULT '',
  category_filter TEXT DEFAULT '',
  location_filter TEXT DEFAULT '',
  limit_count INTEGER DEFAULT 20
)
RETURNS TABLE (
  id UUID,
  username VARCHAR,
  full_name VARCHAR,
  avatar_url TEXT,
  bio TEXT,
  location VARCHAR,
  gender VARCHAR,
  age INTEGER,
  is_verified BOOLEAN,
  average_rating DECIMAL,
  total_reviews INTEGER,
  categories VARCHAR[],
  platforms JSON,
  pricing JSON,
  min_price DECIMAL,
  max_price DECIMAL,
  total_followers BIGINT,
  instagram_followers INTEGER,
  tiktok_followers INTEGER,
  youtube_subscribers INTEGER,
  instagram_handle VARCHAR,
  tiktok_handle VARCHAR,
  youtube_handle VARCHAR,
  price_per_post DECIMAL,
  price_per_story DECIMAL,
  price_per_reel DECIMAL,
  portfolio_urls TEXT[],
  engagement_rate DECIMAL,
  created_at TIMESTAMP WITH TIME ZONE,
  updated_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    isv.id,
    isv.username,
    isv.full_name,
    isv.avatar_url,
    isv.bio,
    isv.location,
    isv.gender,
    isv.age,
    isv.is_verified,
    isv.average_rating,
    isv.total_reviews,
    isv.categories,
    isv.platforms,
    isv.pricing,
    isv.min_price,
    isv.max_price,
    isv.total_followers,
    i.instagram_followers,
    i.tiktok_followers,
    i.youtube_subscribers,
    i.instagram_handle,
    i.tiktok_handle,
    i.youtube_handle,
    i.price_per_post,
    i.price_per_story,
    i.price_per_reel,
    i.portfolio_urls,
    i.engagement_rate,
    isv.created_at,
    isv.updated_at
  FROM influencer_search_view isv
  JOIN influencers i ON isv.id = i.id
  WHERE 
    -- Text search
    (search_term = '' OR isv.search_vector @@ plainto_tsquery('simple', search_term))
    -- Followers filter
    AND isv.total_followers >= min_followers
    AND isv.total_followers <= max_followers
    -- Price filter
    AND (isv.min_price = 0 OR (isv.min_price >= min_price AND isv.min_price <= max_price))
    -- Location filter
    AND (location_filter = '' OR isv.location ILIKE '%' || location_filter || '%')
  ORDER BY 
    CASE 
      WHEN search_term = '' THEN 1.0
      ELSE ts_rank(isv.search_vector, plainto_tsquery('simple', search_term))
    END DESC,
    isv.total_followers DESC
  LIMIT limit_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 4. Grant necessary permissions
GRANT SELECT ON influencer_search_view TO anon, authenticated;
