'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { supabase } from '@/lib/supabase';
import { createPricingPackage } from '@/lib/pricing-packages';
import { Progress } from '@/components/ui/progress';
import { Card, CardContent } from '@/components/ui/card';

// Import step components
import { UsernameStep } from '@/components/onboarding/steps/UsernameStep';
import { AgeStep } from '@/components/onboarding/steps/AgeStep';
import { GenderStep } from '@/components/onboarding/steps/GenderStep';
import { CategoriesStep } from '@/components/onboarding/steps/CategoriesStep';
import { CountryStep } from '@/components/onboarding/steps/CountryStep';
import { CityStep } from '@/components/onboarding/steps/CityStep';
import { BioStep } from '@/components/onboarding/steps/BioStep';

// Import existing components for social media and packages
import { SocialMediaStep } from '@/components/onboarding/SocialMediaStep';
import { PackageStep } from '@/components/onboarding/PackageStep';

interface OnboardingData {
  username: string;
  age: number | null;
  gender: string;
  categories: number[];
  country: string;
  city: string;
  bio: string;
  socialMedia: Array<{
    platform: 'instagram' | 'tiktok' | 'youtube';
    handle: string;
    followers: number;
  }>;
  packages: Array<{
    platform_id: number;
    content_type_id: number;
    quantity: number;
    video_duration?: string;
    price: number;
    platform_name: string;
    platform_icon: string;
    content_type_name: string;
    generated_name: string;
  }>;
}

const ONBOARDING_STEPS = [
  { id: 1, title: 'Username', description: 'Izaberite jedinstveni username' },
  { id: 2, title: 'Godine', description: 'Unesite vaše godine' },
  { id: 3, title: 'Pol', description: 'Izaberite vaš pol' },
  { id: 4, title: 'Kategorije', description: 'Izaberite kategorije sadržaja' },
  { id: 5, title: 'Država', description: 'Izaberite vašu državu' },
  { id: 6, title: 'Grad', description: 'Unesite vaš grad (opcionalno)' },
  { id: 7, title: 'Biografija', description: 'Opišite sebe (opcionalno)' },
  { id: 8, title: 'Društvene mreže', description: 'Dodajte vaše profile' },
  { id: 9, title: 'Paketi', description: 'Kreirajte pakete usluga' },
];

export default function InfluencerOnboardingPage() {
  const { user } = useAuth();
  const router = useRouter();
  const [currentStep, setCurrentStep] = useState(1);
  const [isLoading, setIsLoading] = useState(false);
  const [onboardingData, setOnboardingData] = useState<Partial<OnboardingData>>({
    username: '',
    age: null,
    gender: '',
    categories: [],
    country: '',
    city: '',
    bio: '',
    socialMedia: [],
    packages: [],
  });

  const nextStep = () => {
    if (currentStep < ONBOARDING_STEPS.length) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const updateData = (field: keyof OnboardingData, value: any) => {
    setOnboardingData(prev => ({ ...prev, [field]: value }));
  };

  const handleFinish = async () => {
    if (!user || !onboardingData.username || !onboardingData.age || onboardingData.categories.length === 0 || !onboardingData.country) {
      alert('Molimo popunite sva obavezna polja');
      return;
    }

    setIsLoading(true);

    try {
      // 1. Update profile with basic info
      const profileUpdates: any = {
        username: onboardingData.username,
        age: onboardingData.age,
        gender: onboardingData.gender,
        country: onboardingData.country,
        user_type: 'influencer',
        profile_completed: true,
      };

      if (onboardingData.bio) {
        profileUpdates.bio = onboardingData.bio;
      }

      if (onboardingData.city) {
        profileUpdates.city = onboardingData.city;
      }

      const { error: profileError } = await supabase
        .from('profiles')
        .update(profileUpdates)
        .eq('id', user.id);

      if (profileError) {
        throw new Error('Failed to update profile: ' + profileError.message);
      }

      // 2. Create influencer record if it doesn't exist
      const { data: existingInfluencer } = await supabase
        .from('influencers')
        .select('id')
        .eq('id', user.id)
        .single();

      if (!existingInfluencer) {
        const { error: influencerError } = await supabase
          .from('influencers')
          .insert({
            id: user.id,
            age: onboardingData.age,
            gender: onboardingData.gender,
          });

        if (influencerError) {
          throw new Error('Failed to create influencer record: ' + influencerError.message);
        }
      }

      // 3. Save categories
      if (onboardingData.categories.length > 0) {
        const categoryInserts = onboardingData.categories.map(categoryId => ({
          influencer_id: user.id,
          category_id: categoryId,
          is_primary: false,
        }));

        const { error: categoriesError } = await supabase
          .from('influencer_categories')
          .insert(categoryInserts);

        if (categoriesError) {
          throw new Error('Failed to save categories: ' + categoriesError.message);
        }
      }

      // 4. Save social media handles
      if (onboardingData.socialMedia && onboardingData.socialMedia.length > 0) {
        const platformMapping: { [key: string]: number } = {
          instagram: 1,
          tiktok: 2,
          youtube: 3,
        };

        const socialMediaInserts = onboardingData.socialMedia.map(social => ({
          influencer_id: user.id,
          platform_id: platformMapping[social.platform],
          handle: social.handle,
          followers_count: social.followers,
          is_active: true,
        }));

        const { error: socialError } = await supabase
          .from('influencer_platforms')
          .insert(socialMediaInserts);

        if (socialError) {
          throw new Error('Failed to save social media: ' + socialError.message);
        }
      }

      // 5. Save packages
      if (onboardingData.packages && onboardingData.packages.length > 0) {
        for (const pkg of onboardingData.packages) {
          const result = await createPricingPackage(user.id, {
            platform_id: pkg.platform_id,
            content_type_id: pkg.content_type_id,
            price: pkg.price,
            quantity: pkg.quantity || 1,
            video_duration: pkg.video_duration,
          });

          if (result.error) {
            throw new Error('Failed to create package: ' + result.error.message);
          }
        }
      }

      // Redirect to dashboard
      router.push('/dashboard/influencer');
    } catch (error) {
      console.error('Error saving onboarding data:', error);
      alert('Došlo je do greške. Pokušajte ponovo.');
    } finally {
      setIsLoading(false);
    }
  };

  const renderCurrentStep = () => {
    switch (currentStep) {
      case 1:
        return (
          <UsernameStep
            value={onboardingData.username || ''}
            onChange={(value) => updateData('username', value)}
            onNext={nextStep}
          />
        );
      case 2:
        return (
          <AgeStep
            value={onboardingData.age}
            onChange={(value) => updateData('age', value)}
            onNext={nextStep}
            onBack={prevStep}
          />
        );
      case 3:
        return (
          <GenderStep
            value={onboardingData.gender || ''}
            onChange={(value) => updateData('gender', value)}
            onNext={nextStep}
            onBack={prevStep}
          />
        );
      case 4:
        return (
          <CategoriesStep
            value={onboardingData.categories || []}
            onChange={(value) => updateData('categories', value)}
            onNext={nextStep}
            onBack={prevStep}
          />
        );
      case 5:
        return (
          <CountryStep
            value={onboardingData.country || ''}
            onChange={(value) => updateData('country', value)}
            onNext={nextStep}
            onBack={prevStep}
          />
        );
      case 6:
        return (
          <CityStep
            value={onboardingData.city || ''}
            onChange={(value) => updateData('city', value)}
            onNext={nextStep}
            onBack={prevStep}
          />
        );
      case 7:
        return (
          <BioStep
            value={onboardingData.bio || ''}
            onChange={(value) => updateData('bio', value)}
            onNext={nextStep}
            onBack={prevStep}
          />
        );
      case 8:
        return (
          <SocialMediaStep
            socialMedia={onboardingData.socialMedia || []}
            onUpdate={(socialMedia) => updateData('socialMedia', socialMedia)}
            onNext={nextStep}
            onBack={prevStep}
          />
        );
      case 9:
        return (
          <PackageStep
            packages={onboardingData.packages || []}
            onUpdate={(packages) => updateData('packages', packages)}
            onFinish={handleFinish}
            onBack={prevStep}
            isLoading={isLoading}
          />
        );
      default:
        return null;
    }
  };

  const progressPercentage = (currentStep / ONBOARDING_STEPS.length) * 100;

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-2xl mx-auto px-4">
        {/* Progress bar */}
        <div className="mb-8">
          <div className="flex justify-between items-center mb-2">
            <span className="text-sm font-medium text-gray-600">
              Korak {currentStep} od {ONBOARDING_STEPS.length}
            </span>
            <span className="text-sm text-gray-500">
              {Math.round(progressPercentage)}% završeno
            </span>
          </div>
          <Progress value={progressPercentage} className="h-2" />
        </div>

        {/* Current step */}
        <Card>
          <CardContent className="p-8">
            {renderCurrentStep()}
          </CardContent>
        </Card>

        {/* Step indicator */}
        <div className="mt-6 text-center text-sm text-gray-500">
          {ONBOARDING_STEPS[currentStep - 1]?.title}
        </div>
      </div>
    </div>
  );
}
