# 📋 TASKOVI I PRIORITETI - INFLUENCER MARKETING PLATFORMA

**Kreiran**: 10.08.2025  
**Status**: AKTIVAN  
**Izvor**: Reorganizacija otvoreni-zadaci.md (935 linija)

---

## 🎉 **NEDAVNO RIJEŠENI PROBLEMI**

### ✅ **REAL-TIME CHAT MESSAGES - RIJEŠENO** (10.08.2025)
- **Problem**: Poruke se nisu prikazivale u real-time između korisnika
- **Uzrok**: chat_messages tabela nije bila u Supabase Realtime publikaciji
- **Riješeno**: Dodana tabela u publikaciju + poboljšan event handling
- **Rezultat**: Chat sada radi potpuno u real-time

### ✅ **BUSINESS ONBOARDING - ZAVRŠENO** (06.08.2025)
- Kreiran potpuni business onboarding flow (7 koraka)
- Popravljena CityStep greška + a<PERSON><PERSON>rani tekstovi
- Business profil stranica kompletno ažurirana

### ✅ **AUTHENTICATION & PROFILE SYSTEM - RIJEŠENO** (03.08.2025)
- Database types problem riješen (339→1099 linija)
- Authentication flow potpuno funkcionalan
- Influencer onboarding sistem implementiran (9 koraka)
- Profile stranice ažurirane sa ispravnim podacima

---

## 🚨 **HITNI ZADACI - POTREBNO ODMAH**

### **1. JOB COMPLETION NOTIFICATION ERROR - KRITIČNO** 🔥
**Prioritet**: KRITIČNO  
**Procjena**: 1-2 sata  
**Status**: POTREBNO RIJEŠITI

**Problem**:
```
Error creating notification: Error: supabaseKey is required.
at submitDirectOfferJobCompletion (job-completions.ts:231:45)
```

**Uzrok**: `createServerClient` se poziva u browser environment-u

**Potrebno**:
- [ ] Premjestiti server client pozive - koristiti regular client u browser kodu
- [ ] Provjeriti environment varijable
- [ ] Refaktorisati notification kreiranje - koristiti RPC funkciju
- [ ] Testirati job completion flow

**Fajlovi**: `src/lib/job-completions.ts`, `src/lib/supabase.ts`

---

### **2. DATABASE CLEANUP - HITNO** 🗑️
**Prioritet**: HITNO  
**Procjena**: 2-3 sata  
**Status**: POTREBNO RIJEŠITI

**Problem**: Duplikati i legacy polja u bazi podataka

**Potrebno**:
- [ ] **Ukloniti duplikate iz `influencers` tabele:**
  - `age` → koristiti samo `profiles.age`
  - `gender` → koristiti samo `profiles.gender`
  - Stare handle kolone → koristiti samo `influencer_platforms`
- [ ] **Standardizovati location polja:**
  - Ukloniti `profiles.location` → koristiti `city` + `country`
- [ ] **Očistiti `businesses` tabelu:**
  - `industry` → koristiti samo `business_target_categories`
  - `company_size`, `budget_range` → ukloniti ako se ne koriste
- [ ] **Ažurirati sve funkcije** da koriste nova polja

---

## 🔥 **VISOK PRIORITET**

### **3. FINAL SECURITY AUDIT - VISOK** 🔒
**Prioritet**: VISOK  
**Procjena**: 4-5 sati  
**Status**: POTREBNO RIJEŠITI

**Database security**:
- [ ] **RLS policies audit** - provjeriti sve tabele
- [ ] **Function permissions** - provjeriti sve database funkcije
- [ ] **Data validation** - server-side validation za sve inputs
- [ ] **SQL injection protection** - parameterized queries
- [ ] **Rate limiting** - dodati rate limiting na API endpoints

**Application security**:
- [ ] **Authentication flows** - provjeriti sve auth scenarije
- [ ] **Authorization checks** - user type validations
- [ ] **Input sanitization** - XSS protection
- [ ] **File upload security** - ako imamo file uploads
- [ ] **Environment variables** - provjeriti da nema exposed secrets

---

## 📝 **SREDNJI PRIORITET**

### **4. ESLINT CLEANUP - SREDNJI** 📝
**Prioritet**: SREDNJI  
**Procjena**: 2-3 sata  
**Status**: POTREBNO RIJEŠITI

**Preostale ESLint greške**:
- [ ] **Unused imports** - ukloniti nekorišćene importe kroz codebase
- [ ] **Unused variables** - ukloniti nekorišćene varijable
- [ ] **Remaining `any` types** - zamijeniti preostale `any` tipove
- [ ] **React hooks dependencies** - popraviti useEffect dependency warnings
- [ ] **Prettier formatting** - formatirati preostale fajlove

**Fajlovi sa najviše grešaka**:
- `src/lib/campaigns.ts` - 20+ unused imports/variables
- `src/lib/chat.ts` - 10+ `any` tipovi
- `src/lib/profiles.ts` - unused imports
- `src/app/profil/edit/page.tsx` - missing imports

---

### **5. MOBILE CHAT FULLSCREEN - SREDNJI** 📱
**Prioritet**: SREDNJI  
**Procjena**: 1-2 sata  
**Status**: POTREBNO RIJEŠITI

**Problem**: Chat na mobitelu mora biti fullscreen kada se otvori

**Potrebno**:
- [ ] Analizirati trenutni mobile chat layout
- [ ] Implementirati fullscreen mode za mobile chat
- [ ] Možda sakriti header/navigation na mobile u chat view
- [ ] Dodati back button koji je uvijek vidljiv
- [ ] Optimizovati chat height za mobile (100vh)
- [ ] Testirati na različitim mobile device-ima

---

### **6. USER FLOW ANALIZA I OPTIMIZACIJA - SREDNJI** 🔍
**Prioritet**: SREDNJI  
**Procjena**: 6-8 sati  
**Status**: POTREBNO RIJEŠITI

**Cilj**: Proći cijeli flow oba korisnika od registracije i vidjeti koja polja su zaista potrebna

**Analiza potrebna**:
- [ ] **Registracija flow** - koja polja su obavezna vs opciona
- [ ] **Profile setup** - minimalni vs kompletni profil
- [ ] **Onboarding** - voditi korisnike kroz setup
- [ ] **Required fields validation** - šta je stvarno potrebno za funkcionalnost
- [ ] **Progressive disclosure** - pokazivati polja postupno

**Korisnici za testiranje**:
- Influencer flow: registracija → profil → aplikacija na kampanju → chat → job completion
- Business flow: registracija → profil → kreiranje kampanje → pregled aplikacija → job completion

---

### **7. NOTIFICATION SISTEM POBOLJŠANJA - SREDNJI** 🔔
**Prioritet**: SREDNJI  
**Procjena**: 3-4 sata  
**Status**: POTREBNO RIJEŠITI

**Trenutni problemi**:
- Notifikacije se ne označavaju kao pročitane
- Nema real-time notifikacija
- Nema email notifikacija za važne eventi

**Potrebno**:
- [ ] Mark as read funkcionalnost
- [ ] Real-time notifications sa Supabase
- [ ] Email notifications za kritične eventi
- [ ] Notification preferences u profilu

---

### **8. SEARCH I FILTERING POBOLJŠANJA - SREDNJI** 🔍
**Prioritet**: SREDNJI  
**Procjena**: 4-5 sati  
**Status**: POTREBNO RIJEŠITI

**Marketplace search**:
- [ ] **Full-text search** - bolja pretraga influencera
- [ ] **Advanced filters** - kombinovanje više filtera
- [ ] **Search suggestions** - autocomplete
- [ ] **Search history** - zapamćene pretrage
- [ ] **Saved searches** - mogućnost čuvanja filtera

---

## 🔮 **NIZAK PRIORITET - BUDUĆE POBOLJŠANJA**

### **9. OAUTH ROLE INTEGRATION - NIZAK** 🔐
**Prioritet**: NIZAK  
**Procjena**: 2-3 sata (istraživanje)  
**Status**: ISTRAŽIVANJE POTREBNO

**Napomena**: Provjeriti da li u OAuth možemo ubaciti rolu za premium korisnika

**Potrebno istražiti**:
- [ ] Analizirati Supabase OAuth provider options
- [ ] Provjeriti da li možemo dodati custom claims u OAuth token
- [ ] Istražiti kako implementirati role-based OAuth
- [ ] Dokumentovati mogućnosti za buduće premium features

---

### **10. PRICING MODEL I FREE USER FEATURES - NIZAK** 💰
**Prioritet**: NIZAK  
**Procjena**: 3-4 sata  
**Status**: BUDUĆE PLANIRANJE

**Potrebno definirati**:
- [ ] **Free tier limitations** - koliko kampanja/aplikacija mjesečno
- [ ] **Premium features** - šta dobijaju plaćajući korisnici
- [ ] **Pricing tiers** - Basic, Pro, Enterprise
- [ ] **Payment integration** - Stripe subscription setup
- [ ] **Feature gating** - blokiranje premium funkcija za free usere

**Predlog pricing modela**:
- **Free**: 3 kampanje mjesečno, osnovni chat, osnovni profil
- **Pro**: Unlimited kampanje, advanced analytics, priority support
- **Enterprise**: Custom features, API access, dedicated support

---

### **11. ANALYTICS DASHBOARD - NIZAK** 📊
**Prioritet**: NIZAK  
**Procjena**: 8-10 sati  
**Status**: BUDUĆE FUNKCIONALNOST

**Business analytics**:
- Campaign performance metrics
- ROI tracking
- Influencer performance comparison
- Budget utilization

**Influencer analytics**:
- Earnings overview
- Application success rate
- Rating trends
- Portfolio performance

---

## 🎯 **PRIORITETNI REDOSLIJED IMPLEMENTACIJE**

### **🚨 HITNO (1-2 dana)**:
1. **Job Completion Notification Error** (1-2 sata) - KRITIČNO
2. **Database Cleanup** (2-3 sata) - HITNO

### **🔥 VISOK PRIORITET (1 dan)**:
3. **Final Security Audit** (4-5 sati)

### **📝 SREDNJI PRIORITET (1-2 sedmice)**:
4. **ESLint cleanup** (2-3 sata)
5. **Mobile chat fullscreen** (1-2 sata)
6. **User Flow Analiza** (6-8 sati)
7. **Notification sistem poboljšanja** (3-4 sata)
8. **Search i filtering poboljšanja** (4-5 sati)

### **🔮 NIZAK PRIORITET (buduće)**:
9. **OAuth role integration** (istraživanje)
10. **Pricing model** (planiranje)
11. **Analytics dashboard** (buduća funkcionalnost)

---

## 📊 **PROCJENA VREMENA**

**HITNI ZADACI**: 3-5 sati  
**VISOKI PRIORITET**: 4-5 sati  
**SREDNJI PRIORITET**: 19-28 sati  
**NIZAK PRIORITET**: 13-17 sati  

**UKUPNO**: 39-55 sati za sve zadatke  
**KRITIČNI PUT**: 7-10 sati za najvažnije bugove

---

*Poslednje ažuriranje: 10.08.2025 - Real-time chat riješen, reorganizovani prioriteti*
